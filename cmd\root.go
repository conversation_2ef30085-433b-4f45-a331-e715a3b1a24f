package cmd

import (
	"fmt"
	"goweb/cmd/server"
	"os"

	"github.com/spf13/cobra"
)

var rootWebCmd = &cobra.Command{
	Use:     "web",
	Version: "1.0.0",
	Short:   "Start Web Maybi e-commerce service",
	Long:    `Start Web service`,
}

func init() {
	rootWebCmd.AddCommand(server.ServerCmd)
	rootWebCmd.AddCommand(server.OutEnvCmd)
}

func Execute() {
	if err := rootWebCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
