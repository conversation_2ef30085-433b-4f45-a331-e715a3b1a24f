/**
 * Deals Swiper Initialization
 */
document.addEventListener('DOMContentLoaded', function () {
    const dealsSwiper = new Swiper('.deals-swiper', {
        loop: true,
        centeredSlides: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        spaceBetween: 16,
        breakpoints: {
            320: {
                slidesPerView: 2,
                spaceBetween: 6,
                centeredSlides: false,
            },
            576: {
                slidesPerView: 2,
                spaceBetween: 16,
                centeredSlides: false,
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 16,
                centeredSlides: true,
            },
        }
    });

    /**
     * Deals Event Grid Swiper Initialization
     */
    const dealsEventSwiper = new Swiper('.deals-event__products .swiper', {
        direction: 'horizontal',
        loop: false,
        autoplay: {
            delay: 5000,
            disableOnInteraction: true,
        },
        slidesPerView: 3,
        slidesPerGroup: 6,
        grid: {
            rows: 2,
            fill: 'row',
        },
        spaceBetween: 16,
        pagination: {
            el: '.swiper-pagination',
            clickable: true
        },
        breakpoints: {
            320: {
                slidesPerView: 2,
                slidesPerGroup: 4,
                grid: {
                    rows: 2,
                },
            },
            768: {
                slidesPerView: 3,
                slidesPerGroup: 6,
                grid: {
                    rows: 2,
                },
            },
        }
    });
});
