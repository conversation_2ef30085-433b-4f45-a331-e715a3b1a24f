# maybi-web
Maybi web

# I - Tạo môi trường
	1. install golang >1.23 : https://go.dev/dl/
	2. install docker: https://www.docker.com/products/docker-desktop/
	3. vscode - extentions: golang, vs-icon,html js,rename tag,..
	4. Macbook : brew, zsh, iterm
	5. instal tableplus
    6. migrate github.com/pressly/goose
    7. make file for windown http://gnuwin32.sourceforge.net/packages/make.htm

## II - C<PERSON>u hình env
```bash
cp .env.sample .env
cp .air.server.toml.sample .air.server.toml
```
p/s: C<PERSON>u hình postgres phải giống vs file mongodb docker-compose

# III - Run docker compose (xem cấu hình trong file /docker-compose.yml)
	**Run all** docker-compose up
	1. postgres : docker-compose up -d db
	2. redis : docker-compose up -d redis

## IV - GO MOD 
```bash
go mod init gocms
go mod tidy
go work init 
# go work use ./apps
./migrate
```

## V - Run Migrate --> seeder: path seeder
```bash
make migrate args="up"
make migrate args="seeder up"
make migrate args="down" 
make migrate args="seeder down"
make migrate args="create create_table_post sql"
make migrate args="seeder create insert_post_data sql"
```

**Generate migration : tạo mới migration**
```bash
cd migrate && make migrate args="create create_xyz sql" && cd ..
``` 

## VI - Golang with Air
https://github.com/air-verse/air
```bash
go install github.com/air-verse/air@latest
air -v
curl -sSfL https://raw.githubusercontent.com/air-verse/air/master/install.sh | sh -s
air init
air //(is run)
```
**1. Error (macbook): zsh: command not found: air**
```bash
echo 'export PATH=$PATH:$HOME/go/bin' >> ~/.zshrc
source ~/.zshrc
```

**2. Change path in .air.toml when main.go in /cmd**
```bash
cmd = "go build -o ./tmp/main ." -> cmd = "go build -o ./tmp/main ./cmd"
```

## VII - Golang templ
https://templ.guide
```bash
go install github.com/a-h/templ/cmd/templ@latest
```
Then, create file xyz.templ
```
go get github.com/a-h/templ
templ generate
```

*** fix macbook ***
```bash
go install github.com/a-h/templ/cmd/templ@latest
 GOBIN=$HOME/go/bin go install github.com/a-h/templ/cmd/templ@latest
 which templ
  export GOPATH="$HOME/go"
  export GOBIN="$GOPATH/bin"
  export PATH="$PATH:$GOBIN"
 source ~/.zshrc
 go install github.com/a-h/templ/cmd/templ@latest
 which templ
```

### Tích hợp Air và Templ
Sửa file .air.toml
```bash
  cmd = "templ generate && go build -o ./tmp/main ."
  exclude_regex = ["_test.go",".*_templ.go"]
  include_ext = ["go", "tpl", "tmpl", "html", "templ"]
```

https://github.com/a-h/templ/blob/main/examples/blog/posts.templ

### Layout
```bash
templ Layout() {
    <!DOCTYPE html>
    <html lang="en">
        <head>
            <meta charset="UTF-8">
            <title>{title}</title>
        </head>
        <body>
            <main>
               { children... }
            </main>
        </body>
    </html>
}
```
```bash
import "admin/views/layouts"

templ Dashboard() {
    @layouts.Layout("Dashboard") {
		<div>Welcome to my website.</div>
	}
}
```