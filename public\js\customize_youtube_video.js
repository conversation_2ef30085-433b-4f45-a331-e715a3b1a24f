let player;

function onYouTubeIframeAPIReady() {
  player = new YT.Player("player", {
    videoId: "hPXejO5UTNk",
    events: {
      onStateChange: onPlayerStateChange,
    },
    playerVars: {
      autoplay: 0,
      mute: 1,
      rel: 0,
      playsinline: 1,
    },
  });
}

function onPlayerStateChange(event) {
  const btn = document.getElementById("play-toggle-btn");
  const playerContainer = document.getElementById("player-container");
  switch (event.data) {
    case YT.PlayerState.PLAYING:
      playerContainer.style.opacity = "1";
      playerContainer.style.transition = "all 0.5s ease";
      playerContainer.style.visibility = "visible";
      break;
    case YT.PlayerState.PAUSED:
      playerContainer.style.opacity = "0";
      playerContainer.style.transition = "all 0.5s ease";
     playerContainer.style.visibility = "hidden";

      break;
    case YT.PlayerState.ENDED:
      playerContainer.style.opacity = "0";
      playerContainer.style.transition = "all 0.5s ease";
      playerContainer.style.visibility = "hidden";
      break;
  }
}

document
  .getElementById("play-toggle-btn")
  .addEventListener("click", function () {
    const state = player.getPlayerState();
    if (state === YT.PlayerState.PLAYING) {
      player.pauseVideo();
    } else {
      player.playVideo();
    }
  });

document.addEventListener("click", function (event) {
  const playerContainer = document.getElementById("player-container");
  if (!playerContainer.contains(event.target)) {
    if (player && typeof player.pauseVideo === "function") {
      const state = player.getPlayerState();
      if (state === YT.PlayerState.PLAYING) {
        player.pauseVideo();
      }
    }
  }
});
