package internal

import (
	"goweb/frontend"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/compress"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/networld-solution/sctx"
	"github.com/networld-solution/sctx/configs"
	"github.com/networld-solution/sctx/midd"
)

func RoutesServer(app *fiber.App, serviceCtx sctx.ServiceContext) {
	app.Use(logger.New(logger.Config{
		Format: `{"ip":${ip}, "timestamp":"${time}", "status":${status}, "latency":"${latency}", "method":"${method}", "path":"${path}"}` + "\n",
	}))
	app.Use(compress.New())
	app.Use(cors.New())

	if serviceCtx.EnvName() == configs.AppProd {
		app.Use(midd.Recovery(serviceCtx))
	}

	app.Static("/static", "./public")

	SetupRoutes(app, serviceCtx)

}

func SetupRoutes(app *fiber.App, serviceCtx sctx.ServiceContext) {
	frontend.SetupRoutes(app, serviceCtx)

}
