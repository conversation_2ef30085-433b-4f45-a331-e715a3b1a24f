version: "3.9"

services:
  mongodb:
    image: 'mongo:5.0'
    container_name: 'mongo-ecom-maybi'
    volumes:
      - ./docker/mongo/data:/data/db
      - ./docker/mongo/init/:/docker-entrypoint-initdb.d/
    restart: always
    ports:
      - '${MONGO_PORT}:27017'
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE}
  redis:
    container_name: 'redis-ecom-maybi'
    image: redis:7.2.4
    volumes:
      - ./docker/redis:/etc/redis
    command: [ 'redis-server', '/etc/redis/redis.conf' ]
    ports:
      - '${REDIS_PORT}:6379'