package terms

import (
	"github.com/networld-solution/gos/templates"
	"goweb/views/terms/components"
	"goweb/views/layouts"
)

templ Terms() {
	@layouts.Master(nil, &[]templ.Component{headTerms()}, scriptTerms()) {
		@components.TermsBackground()
		@components.TermsContent()
	}
}

templ headTerms() {
	<link rel="stylesheet" href={ templates.AssetURL("/static/css/terms.css") }/>
}

templ scriptTerms() {
	<script type="text/javascript" src={templates.AssetURL("/static/js/maybi-ui.js")}></script>
	<script type="text/javascript" src={templates.AssetURL("/static/js/terms.js")}></script>
}
