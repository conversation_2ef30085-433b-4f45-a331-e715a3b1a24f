.cart__list{
    margin-top: 3em;
}

.cart__item{
    display: flex;
    position: relative;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 2em;
    border-bottom: 0.1em solid rgba(0, 0, 0, 0.2);
    justify-content: space-between;
    gap: 2em;
}
.cart__item__img{
    width: 20%;
    border-radius: 0.8em;
    height: 15em;
    overflow: hidden;
}   

.cart__item__content{
    width: 70%;
    gap: 1em;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.cart__item__title{
    font-size: 1.6em;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    transition: all 0.3s ease;
} 


.cart__item__title:hover{
    color: var(--primary-hover)
} 

.cart__item__title > a{
    font-size: 1em;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    transition: all 0.3s ease;
}

.cart__item__info{
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2em;
}

.cart__item__price{
    font-size: 1.4em;
    font-weight: 600;
    color: var(--primary);
}

.cart__item__delete{
    width: 10%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: var(--10px);
}

.cart-icon__delete{
    width: 2em;
    height: 2em;
}

.cart__total{
    display: flex;
    justify-content: space-between;
    padding-bottom: 2em;
    margin-bottom: 2em;
    border-bottom: 0.1em solid rgba(0, 0, 0, 0.2);
}
.cart__total__item{
    font-size: 1.6em;
    font-weight: 600;
}
.cart__action{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2em;
}

.cart__action__item{
    width: 50%;
}

.cart__item__variants{
    display: flex;
    gap: 1em;
    align-items: center;
}


.cart__item__variants > .product-size{
    opacity: unset;
    flex-direction: row;
    width: 100%;
    position: unset;
    background-color: transparent;
    justify-content: flex-start;
}

.cart__item__variants > span{
    font-size: 1.4em;
    width: fit-content;
} 

.cart__item__content.product__info{
    padding: 0;
    background-color: transparent;
    box-shadow: none;
}

.quantity-cart{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5em;
}

.quantity-cart__btn{
    border: .1em solid var(--black);
    background-color: var(--black);
    color: var(--white);
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-cart__btn >.product-icon__down, 
.quantity-cart__btn >.product-icon__up{
  font-size: var(--10px);
  width: 2.7em;
  height: 2.7em;
  fill: var(--white);
}

.quantity-cart__input{
  padding: 0.5em;
  font-size: var(--10px);
  text-align: center;
  border: .1em solid var(--black);
  width: 2.8em;
  height: 2.8em;
  border-radius: 50%;
}

.cart__total__price{
    color: var(--primary);
}

.btn-cart{
    font-size: 1.5em;
    height: 2.9em;
    border: none;
}