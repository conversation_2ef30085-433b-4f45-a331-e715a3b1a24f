package blogs

import (
	"github.com/networld-solution/gos/templates"
	"goweb/views/blogs/components"
	"goweb/views/layouts"
)

templ Blogs() {
	@layouts.Master(nil, &[]templ.Component{headBlogs()}, scriptBlogs()) {
		@components.BlogsBackground()
		@components.BlogsMenuSubCpn()
		@components.BlogsNewCpn()
		@components.BlogsViewsCpn()

		<section class="container90 ana-main">
			<div class="ana-main__wrapper">
				<div class="wrap__left">
					@components.BlogsXuHuongThoiTrangCpn()
					@components.BlogsPhongCachCpn()
				</div>
				<div class="wrap__right">
					@components.BlogRightBannerCpn()
				</div>
			</div>
		</section>
	}
}

templ headBlogs() {
	<link rel="stylesheet" href={ templates.AssetURL("/static/css/blogs.css") }/>
}

templ scriptBlogs() {
	<script type="text/javascript" src={ templates.AssetURL("/static/js/maybi-ui.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/js/blogs.js") }></script>
}
