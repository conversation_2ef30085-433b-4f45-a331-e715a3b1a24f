package blogs

import (
"github.com/networld-solution/gos/templates"
"goweb/views/blogs/components"
"goweb/views/layouts"
)

templ Blogs() {
@layouts.Master(nil, &[]templ.Component{headBlogs()}, scriptBlogs()) {
@components.BlogsBackground()
@components.BlogsMenuSubCpn()
@components.BlogsNewCpn()
@components.BlogsViewsCpn()

<section class="container90 ana-main">
	<div class="ana-main__wrapper">
		<div class="wrap__left">
			@components.BlogsXuHuongThoiTrangCpn()
			@components.BlogsPhongCachCpn()
		</div>
		<div class="wrap__right">
			@components.BlogRightBannerCpn()
		</div>
	</div>
</section>
}
}

templ headBlogs() {
<link rel="stylesheet" href={ templates.AssetURL("/static/css/blogs.css") } />
}

templ scriptBlogs() {
<script type="text/javascript" src={ templates.AssetURL("/static/js/maybi-ui.js") }></script>
<script type="text/javascript" src={ templates.AssetURL("/static/js/blogs.js") }></script>
// JS Scroll Section
<script>
	document.querySelectorAll('.menubrc-item').forEach(item => {
		item.addEventListener('click', function () {
			const targetSelector = this.getAttribute('data-target');
			const targetElement = document.querySelector(targetSelector);

			if (targetElement) {
				const offset = 140; // chiều cao menu
				const elementPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
				const offsetPosition = elementPosition - offset;

				window.scrollTo({
					top: offsetPosition,
					behavior: 'smooth'
				});

				history.replaceState(null, null, ' ');
			}
		});
	});
</script>

// JS Show More Section
<script>
	document.addEventListener('DOMContentLoaded', function () {
		// Handle show more for both blog sections
		document.querySelectorAll('.menubrc-wrapper').forEach(wrapper => {
			const articles = wrapper.parentElement.querySelectorAll('.ana-articles .nitem');
			const btn = wrapper.querySelector('.btn-show-more');
			const menuLine = wrapper.querySelector('.menu-line');

			// Initially hide articles after index 3 (show first 4)
			articles.forEach((article, index) => {
				if (index >= 4) {
					article.classList.add('hidden');
				}
			});

			// Add click event to show more button
			if (btn) {
				btn.addEventListener('click', function () {
					articles.forEach(article => article.classList.remove('hidden'));
					btn.style.display = 'none';
					menuLine.style.display = 'none';
				});
			}
		});
	});
</script>
}