package blogs

import (
	"github.com/networld-solution/gos/templates"
	"goweb/views/blogs/components"
	"goweb/views/layouts"
)

templ Blogs() {
	@layouts.Master(nil, &[]templ.Component{headBlogs()}, scriptBlogs()) {
		@components.BlogsBackground()
		@components.BlogsMenuSubCpn()
		@components.BlogsNewCpn()
		@components.BlogsViewsCpn()

		<section class="container90 ana-main">
			<div class="ana-main__wrapper">
				<div class="wrap__left">
					@components.BlogsXuHuongThoiTrangCpn()
					@components.BlogsPhongCachCpn()
				</div>
				<div class="wrap__right">
					@components.BlogRightBannerCpn()
				</div>
			</div>
		</section>
	}
}

templ headBlogs() {
	<link rel="stylesheet" href={ templates.AssetURL("/static/css/blogs.css") }/>
}

templ scriptBlogs() {
	<script type="text/javascript" src={ templates.AssetURL("/static/js/maybi-ui.js") }></script>
	<script type="text/javascript" src={ templates.AssetURL("/static/js/blogs.js") }></script>
	// JS Scroll Section
    <script>
        document.querySelectorAll('.menubrc-item').forEach(item => {
            item.addEventListener('click', function () {
                const targetSelector = this.getAttribute('data-target');
                const targetElement = document.querySelector(targetSelector);

                if (targetElement) {
                    const offset = 140; // chiều cao menu
                    const elementPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                    const offsetPosition = elementPosition - offset;

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });

                    history.replaceState(null, null, ' ');
                }
            });
        });
    </script>

// JS Show More Section
	<script>
		document.addEventListener('DOMContentLoaded', function() {
			const articles2 = document.querySelectorAll('.ana-articles-2 .nitem-2');
			const btn2 = document.querySelector('.btn-show-more-2');
			const menuLine2 = document.querySelector('.menu-line-2');
			articles2.forEach((article2, index) => {
				if (index >= 4) {
					article2.classList.add('hidden');
				}
			});
			btn2.addEventListener('click', function () {
				articles2.forEach(article2 => article2.classList.remove('hidden'));
				btn2.style.display = 'none';
				menuLine2.style.display = 'none';
			});
		});
	</script>
	<script>
		document.addEventListener('DOMContentLoaded', function() {
			const articles = document.querySelectorAll('.ana-articles .nitem');
			const btn = document.querySelector('.btn-show-more');
			const menuLine = document.querySelector('.menu-line');
			articles.forEach((article, index) => {
				if (index >= 4) {
					article.classList.add('hidden');
				}
			});
			btn.addEventListener('click', function () {
				articles.forEach(article => article.classList.remove('hidden'));
				btn.style.display = 'none';
				menuLine.style.display = 'none';
			});
		});
	</script>
}
