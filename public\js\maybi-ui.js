const maybiUI = (function () {
  const alertBox = document.getElementById('customAlert');
  const alertMsg = document.getElementById('alertMessage');
  const alertClose = document.getElementById('alertClose');

  function openModal(id) {
    const modal = document.getElementById(id);
    if (modal && modal.classList.contains('modal__overlay')) {
      modal.classList.add('is-open');
    }

    const onClickOutside = (e) => {
      if (e.target === modal) {
        modal.classList.remove("is-open");
        modal.removeEventListener("click", onClickOutside);
      }
    };

    modal.addEventListener("click", onClickOutside);
  }

  function closeModal(id) {
    const modal = document.getElementById(id);
    if (modal && modal.classList.contains('modal__overlay')) {
      modal.classList.remove('is-open');
    }
  }


  function showAlert(message, duration = 3000) {
    if (!alertBox || !alertMsg || !alertClose) return;

    alertMsg.textContent = message;
    alertBox.style.display = 'flex';

    const timeout = setTimeout(() => {
      alertBox.style.display = 'none';
    }, duration);

    alertClose.onclick = () => {
      alertBox.style.display = 'none';
      clearTimeout(timeout);
    };
  }

  function createDialog({
    title = '',
    content = '',
    confirmText = 'Đồng ý',
    cancelText = 'Hủy',
    closable = true,
    onConfirm = null,
  } = {}) {
    const overlay = document.createElement('div');
    overlay.className = 'dialog__overlay';
    overlay.setAttribute('data-closable', closable ? 'true' : 'false');

    overlay.innerHTML = `
      <div class="dialog">
        <button class="dialog__close" aria-label="Đóng">&times;</button>
        <div class="dialog__title">${title}</div>
        <div class="dialog__content">${content}</div>
        <div class="dialog__actions">
          <button class="button--text dialog__cancel">${cancelText}</button>
          <button class="button button--primary dialog__confirm">${confirmText}</button>
        </div>
      </div>
    `;

    document.body.appendChild(overlay);

    const closeBtn = overlay.querySelector('.dialog__close');
    const cancelBtn = overlay.querySelector('.dialog__cancel');
    const confirmBtn = overlay.querySelector('.dialog__confirm');

    const closeDialog = () => overlay.remove();

    overlay.addEventListener('click', (e) => {
      if (e.target === overlay && closable) {
        closeDialog();
      }
    });

    closeBtn.addEventListener('click', closeDialog);
    cancelBtn.addEventListener('click', closeDialog);

    confirmBtn.addEventListener('click', () => {
      if (onConfirm) onConfirm();
      closeDialog();
    });

    overlay.style.display = 'flex';
  }


  function flashSale() {
    const timers = document.querySelectorAll('[data-flashsale]');

    timers.forEach(timer => {
      const timeEnd = timer.getAttribute('data-time-end');
      if (!timeEnd) return;

      const end = new Date(timeEnd).getTime();
      let interval; 

      const update = () => {
        const now = new Date().getTime();
        const distance = end - now;

        if (distance <= 0) {
          timer.textContent = 'Đã kết thúc';
          clearInterval(interval); 
          return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);


        timer.innerHTML = `
          ${days > 0 ? `<span class="countdown__days countdown__item">${days}</span><span class="countdown__separator">:</span>` : ''}
          <span class="countdown__hours countdown__item">${pad(hours)}</span>
          <span class="countdown__separator">:</span>
          <span class="countdown__minutes countdown__item">${pad(minutes)}</span>
          <span class="countdown__separator">:</span>
          <span class="countdown__seconds countdown__item">${pad(seconds)}</span>
      `;
      };

      const pad = (num) => String(num).padStart(2, '0');

      update(); // render lần đầu
      interval = setInterval(update, 1000); // ✅ gán sau khi đã khai báo
    });
  }

  function openSeed(id) {
    const sheet = document.getElementById(id);
    if (sheet && sheet.classList.contains("sheet__overlay")) {
      sheet.classList.add("is-open");
    }

    const onClickOutside = (e) => {
      if (e.target === sheet) {
        sheet.classList.remove("is-open");
        sheet.removeEventListener("click", onClickOutside);
      }
    };

    sheet.addEventListener("click", onClickOutside);
  }

  function closeSeed(id) {
    const sheet = document.getElementById(id);
    if (sheet && sheet.classList.contains("sheet__overlay")) {
      sheet.classList.remove("is-open");
    }
  }

  return {
    showAlert,
    createDialog,
    openModal,
    closeModal,
    flashSale,
    openSeed,
    closeSeed
  };
})();

window.onload = function() {
 var promotion = document.getElementById('promotion-modal');
  if(promotion) {
    promotion.click();
    maybiUI.openModal('promotion-modal');
  }
};
