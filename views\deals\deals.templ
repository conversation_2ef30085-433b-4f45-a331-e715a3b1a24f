package deals

import (
	"github.com/networld-solution/gos/templates"
	"goweb/views/deals/components"
	"goweb/views/layouts"
	"goweb/views/partials"
)

templ Deals() {
	@layouts.Master(nil, &[]templ.Component{headDeals()}, scriptDeals()) {
		@components.DealsBackground()
		@components.ListDeals()
		@partials.OffcanvasCart()
	}
}

templ headDeals() {
	<link rel="stylesheet" href={ templates.AssetURL("/static/libs/swiper/swiper-bundle.min.css") }/>
	<link rel="stylesheet" href={templates.AssetURL("/static/css/mega-voucher.css")}>
	<link rel="stylesheet" href={templates.AssetURL("/static/css/flashsale.css")}>
	<link rel="stylesheet" href={ templates.AssetURL("/static/css/deals.css") }/>
}

templ scriptDeals() {
	<script type="text/javascript" src={templates.AssetURL("/static/js/maybi-ui.js")}></script>
	<script type="text/javascript" src={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.js")}></script>
	<script type="text/javascript" src={templates.AssetURL("/static/js/deals.js")}></script>
	<script type="text/javascript" src={templates.AssetURL("/static/js/flash-sale.js")}></script>
}