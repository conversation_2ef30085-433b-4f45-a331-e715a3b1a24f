APP_ENV=dev
LOG_LEVEL=trace
ENV_FILE=.env

LOG_DAILY=true
LOG_PATH=logs
MAX_SIZE=300
MAX_AGE=5
MAX_BACKUPS=5
COMPRESS=true
SESSION_LIFETIME=120

FIBER_MODE=release
FIBER_PORT=7002

DOMAIN=http://localhost:${FIBER_PORT}
LOCATION=Asia/Ho_Chi_Minh

MONGO_HOST=localhost
MONGO_PORT=27021
MONGO_USERNAME=root
MONGO_PASSWORD=123456xyz
MONGO_DATABASE=ecom-maybi
MONGO_DSN=mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@${MONGO_HOST}:${MONGO_PORT}

REDIS_PORT=6320
REDIS_HOST=127.0.0.1:${REDIS_PORT}
REDIS_DB=0
REDIS_PASSWORD=123456Xyz
REDIS_USERNAME=ecom-maybi
REDIS_URI=redis://${REDIS_USERNAME}:${REDIS_PASSWORD}@${REDIS_HOST}
# REDIS_URI=redis://${REDIS_HOST}
REDIS_CACHE=60

DISCORD_TOKEN=
DISCORD_DEV_CHANNEL_IDS=