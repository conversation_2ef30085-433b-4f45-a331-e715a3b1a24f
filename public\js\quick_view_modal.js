const quickViewModal = (function () {
  let modal, closeBtn, quickViewSwiper;

  function initModal() {
    modal = document.getElementById("product-modal");
    closeBtn = document.getElementById("modal-close");

    if (!modal || !closeBtn) return;

    document.querySelectorAll(".btn-quick-view").forEach((btn) => {
      btn.addEventListener("click", openModal);
    });

    closeBtn.addEventListener("click", closeModal);
    modal.addEventListener("click", (e) => {
      if (e.target.classList.contains("product-modal__overlay")) {
        closeModal();
      }
    });
  }

  function openModal() {
    modal.classList.remove("product-modal--hidden");
    document.body.classList.add("modal-open");
  }

  function closeModal() {
    modal.classList.add("product-modal--hidden");
    document.body.classList.remove("modal-open");
  }

  function initQuickViewSwiper() {
    quickViewSwiper = new Swiper(".swiper-quick-view", {
      slidesPerView: 1,
      spaceBetween: 8,
    });

    document.querySelectorAll(".product-thumbnails img").forEach((thumb, index) => {
      thumb.addEventListener("click", () => {
        quickViewSwiper.slideTo(index);
        document.querySelectorAll(".product-thumbnails img")
          .forEach((img) => img.classList.remove("active"));
        thumb.classList.add("active");
      });
    });
  }

  function formatModalInfo() {
    document.querySelectorAll(".product-modal__info p").forEach((p) => {
      const links = p.querySelectorAll("a");
      links.forEach((link, index) => {
        if (index < links.length - 1) {
          link.insertAdjacentText("afterend", ",");
        }
      });
    });
  }

  function initFeatureSwiper() {
    const slideCount = document.querySelectorAll(".swiper-feature-offer .swiper-slide").length;
    const enableLoop = slideCount >= 3;

    new Swiper(".swiper-feature-offer", {
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      navigation: {
        nextEl: ".collection__next",
        prevEl: ".collection__prev",
      },
      speed: 1200,
      loop: enableLoop,
      breakpoints: {
        0: {
          slidesPerView: 1,
        },
        768: {
          slidesPerView: 3,
        },
      },
    });
  }

  return {
    init: function () {
      initModal();
      initQuickViewSwiper();
      formatModalInfo();
      initFeatureSwiper();
    }
  };
})();

document.addEventListener("DOMContentLoaded", function () {
  quickViewModal.init();
});