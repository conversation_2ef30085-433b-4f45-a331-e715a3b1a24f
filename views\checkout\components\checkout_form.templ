package components

import (
	"github.com/networld-solution/gos/templates"
)

templ CheckoutFormCpn(){
<div class="checkout__left">
    <div class="checkout__main-header">
        <a href="/" class="logo">
            <h1 class="logo-text">MAYBI - Thời trang nữ thiết kế - Ch<PERSON>t lư<PERSON> v<PERSON><PERSON>t trội - <PERSON><PERSON><PERSON> cả hợp lý</h1>
        </a>
        <ul class="checkout__breadcrumb">
            <li class="checkout__breadcrumb-item">
                <a href="/cart">Giỏ hàng</a>
            </li>
            <li class="checkout__breadcrumb-item breadcrumb-item-current">
                <span>Thông tin giao hàng</span>
            </li>
        </ul>
    </div>
    @mainContentLeftPc()
    @summaryMobile()
    // @btnCheckoutMobile()
</div>
}

templ summaryMobile(){
    <div class="checkout__summary__mobile">
        <div class="checkout__order-detail">
            <ul class="order-detail__list">
                <li class="order-detail__item">
                    <div>
                        <div class="order-detail__item-thumb">
                            <img src={templates.AssetURL("/static/images/blazer-black-2.jpg")} alt="Áo blazer form rộng">
                            <span class="order-detail__item-quantity">1</span>
                        </div>
                    </div>
                    <div class="order-detail__item-info">
                        <div class="order__item-info__box">
                            <h6>Áo blazer form rộng</h6>
                            <span class="order__item-info__size">S</span>
                            <span class="order__item-info__color">Đen</span>
                        </div>
                        <div class="order__item-price">
                            <span>77.000đ</span>
                            <span>469.000đ</span>
                        </div>
                    </div>
                </li>
                <li class="order-detail__item">
                    <div>
                        <div class="order-detail__item-thumb">
                            <img src={templates.AssetURL("/static/images/blazer-blue.jpg")} alt="Áo blazer form rộng">
                            <span class="order-detail__item-quantity">1</span>
                        </div>
                    </div>
                    <div class="order-detail__item-info">
                        <div class="order__item-info__box">
                            <h6>Áo blazer form rộng</h6>
                            <span class="order__item-info__size">L</span>
                            <span class="order__item-info__color">Xanh dương</span>
                        </div>
                        <div class="order__item-price">
                            <span>77.000đ</span>
                            <span>469.000đ</span>
                        </div>
                    </div>
                </li>
            </ul>
            <div class="order__shipping-method">
                <h2>Phương thức vận chuyển</h2>
                <div class="shipping-method__wrapper">
                    <div class="shipping-method__checkbox">
                        <input class="form-check-input radio" type="radio" id="flexRadioDefault5" name="shipping_method_mobile" checked>
                        <label class="form-check-label" for="flexRadioDefault5">Giao hàng tận nơi - Thu tiền sau (COD)</label>
                    </div>
                    <span>30.000đ</span>
                </div>
            </div>
            <div class="checkout__payment-method__box">
                <h2>Phương thức thanh toán</h2>
                <ul class="checkout__payment-methods">
                    <li class="payment-method__item">
                        <div class="payment-method__header">
                            <div class="payment-method__checkbox">
                                <input type="radio" name="payment_method_mobile" class="payment-radio" id="flexRadioDefault1">
                                <label for="flexRadioDefault1" class="payment-label">Chuyển khoản ngân hàng</label>
                            </div>
                        </div>
                        <div class="payment-method__content">
                            <div class="payment-method__body">
                                <p>Hãy chuyển khoản trực tiếp vào tài khoản ngân hàng của chúng tôi. Vui lòng sử dụng Mã đơn hàng của bạn làm nội dung chuyển khoản. Đơn hàng sẽ không được giao cho đến khi tiền được ghi nhận vào tài khoản của chúng tôi.
                                </p>
                            </div>
                        </div>
                    </li>
                    <li class="payment-method__item">
                        <div class="payment-method__checkbox">
                            <input class="form-check-input radio" type="radio" id="flexRadioDefault2" name="payment_method_mobile" checked>
                            <svg>
                                <use href="#icon-payment-cod"></use>
                            </svg>
                            <label class="form-check-label" for="flexRadioDefault2">Thanh toán khi nhận hàng (COD)</label>
                        </div>
                    </li>
                    <li class="payment-method__item">
                        <div class="payment-method__checkbox">
                            <input class="form-check-input radio" type="radio" id="flexRadioDefault3" name="payment_method_mobile">
                            <svg>
                                <use href="#icon-vn-pay"></use>
                            </svg>
                            <label class="form-check-label" for="flexRadioDefault3">Thẻ ATM/Visa/Master/JCB/QR Pay qua cổng VNPAY</label>
                        </div>
                    </li>
                    <li class="payment-method__item">
                        <div class="payment-method__checkbox">
                            <input class="form-check-input radio" type="radio" id="flexRadioDefault4" name="payment_method_mobile">
                            <svg>
                                <use href="#icon-momo"></use>
                            </svg>
                            <label class="form-check-label" for="flexRadioDefault4">Ví MoMo</label>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="order-detail__summary">
                <div class="order__summary-discount">
                    <div class="summary-discount__box-content">
                        <div class="summary-discount__content">
                            <input placeholder=" " class="discount-input" size="30" type="text" id="discount_code" name="discount_code" value="">
                            <label class="discount-label" for="discount_code">Mã giảm giá</label>
                        </div>
                        <button type="submit" class="summary-discount__btn-submit" disabled>
                            <span class="btn-content">Sử dụng</span>
                        </button>
                    </div>
                    <div class="discount__choose-coupons btn-open-vouchers">
                        <div class="discount__choose-coupons__content">
                            <svg>
                                <use href="#icon-voucher"></use>
                            </svg>
                            <span>Xem thêm mã giảm giá</span>
                        </div>
                        <div class="cart-vouchers__selected">
                            <div class="cart-vouchers__item freeship">
                                <span class="cart-vouchers__chip freeship">
                                    <span>Giảm 18%</span>
                                </span>
                            </div>
                            <div class="cart-vouchers__item discount">
                                <span class="cart-vouchers__chip discount">
                                    <span>Giảm 10%</span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="order__summary-subtotal">
                    <span>Tạm tính</span>
                    <span data-type="subtotal">184.000đ</span>
                </div>
                <div class="order__summary-shipping">
                    <span>Phí vận chuyển</span>
                    <span>30.000đ</span>
                </div>
                <div class="order__summary-discount-price">
                    <span>Mã giảm giá</span>
                    <span data-type="discount">-30.000đ</span>
                </div>
                <div class="order__summary-total">
                    <span>Tổng cộng</span>
                    <span data-type="total">184.000đ</span>
                </div>
            </div>
            <div class="checkout__btn__mobile">
                <div class="checkout__privacy-note">
                    <p class="text">
                        Your personal data will be used to process your order, support your experience throughout this website, and for other purposes described in our
                        <a href="#">privacy policy.</a>
                    </p>
                </div>
                <div class="checkout__form-group">
                    <div class="form__checkbox-wrapper">
                        <input type="checkbox" class="form__checkbox" id="checkboxTerms" checked>
                        <label class="form__checkbox-label" for="checkboxTerms">
                        Tôi đã đọc và đồng ý với các điều khoản và điều kiện của Maybi.
                        </label>
                    </div>
                </div>
                <a href="#" class="btn btn--primary checkout_btn"><span>Thanh toán</span></a>
            </div>
        </div>
    </div>
}

templ mainContentLeftPc(){
    <div class="checkout__main-content">
        <div class="checkout__form">
            <div class="form__header">
                <h2>Thông tin giao hàng</h2>
            </div>
            <div class="form__content form__customer__info">
                <div class="form__auth-redirect">
                    <p class="form__content-text">Bạn đã có tài khoản?</p>
                    <a href="/">Đăng nhập</a>
                </div>

                <div class="customer__info__box">
                    <div class="customer__name customer__inuput-field">
                        <input placeholder=" " type="text" value="" id="customer_name" name="customer_name" required>
                        <label for="customer_name" class="shipping__label">Họ và tên</label>
                    </div>
                    <div class="customer__email-phone__box">
                        <div class="customer__phone customer__inuput-field">
                            <input placeholder=" " value="" id="customer_phone" type="tel" name="customer_phone" maxlength="15" required>
                            <label for="customer_phone" class="shipping__label">Số điện thoại</label>
                        </div>
                        <div class="customer__email customer__inuput-field">
                            <input placeholder=" " type="email" value="" id="customer_email" name="customer_email">
                            <label for="customer_email" class="shipping__label">Email</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form__content">
                <div class="form__content__address">
                    <div class="customer__street customer__inuput-field">
                        <input placeholder=" " value="" type="text" id="customer_street" name="customer_street" maxlength="15" required>
                        <label for="customer_street" class="shipping__label">Địa chỉ</label>
                    </div>
                    <div class="customer__shipping__group">
                        <div class="shipping__province">
                            <label class="shipping__label" for="customer_shipping_province">Tỉnh / thành</label>
                            <select name="customer_shipping_province">
                                <option data-code="null" value="null" selected>Chọn tỉnh / thành</option>
                                <option data-code="HC" value="50" >Hồ Chí Minh</option>
                                <option data-code="HI" value="1">Hà Nội</option>
                                <option data-code="DT" value="56">Đồng Tháp</option>
                            </select>
                        </div>
                        <div class="shipping__district">
                            <label class="shipping__label" for="customer_shipping_district">Quận / huyện</label>
                            <select  name="customer_shipping_district">
                                <option data-code="null" value="null" selected>Chọn quận / huyện</option>
                                <option data-code="HC466" value="466">Quận 1</option>
                                <option data-code="HC475" value="475">Quận 10</option>
                                <option data-code="HC470" value="470">Quận 5</option>
                            </select>
                        </div>
                        <div class="shipping__ward">
                            <label class="shipping__label" for="customer_shipping_ward">Phường / xã</label>
                            <select name="customer_shipping_ward">
                                <option data-code="null" value="null" selected>Chọn phường / xã</option>
                                <option data-code="26896" value="26896">Phường 01</option>
                                <option data-code="26884" value="26884">Phường 10</option>
                                <option data-code="26902" value="26902">Phường 03</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form__note-wrapper">
                <div class="form__note">
                    <textarea id="note_content" name="note_content" placeholder=" " rows="3"></textarea>
                    <label for="note_content" class="note_label">Lời nhắn cho người bán</label>
                </div>
            </div>
            <div class="form__content">
                <div class="form__order-for-others">
                    <div class="form__order-toggle">
                        <input type="checkbox" id="toggle_order_for_others">
                        <label for="toggle_order_for_others">Đặt hàng giúp người khác</label>
                    </div>
                    <div class="order-for-others__content" hidden aria-hidden="true">
                        <div class="recipient__name customer__inuput-field">
                            <input placeholder=" " type="text" name="recipient_name" id="recipient_name">
                            <label for="recipient_name" class="shipping__label">Tên người nhận</label>
                        </div>
                        <div class="customer__phone-address__box">
                            <div class="recipient__phone customer__inuput-field">
                                <input placeholder=" " type="tel" name="recipient_phone" id="recipient_phone">
                                <label for="recipient_phone" class="shipping__label">Số điện thoại người nhận</label>
                            </div>
                            <div class="recipient_street customer__inuput-field">
                                <input placeholder=" " type="text" id="recipient_street" name="recipient_street" maxlength="100">
                                <label for="recipient_street" class="shipping__label">Địa chỉ người nhận</label>
                            </div>
                        </div>

                        <div class="customer__shipping__group">
                            <div class="shipping__province">
                                <label class="shipping__label" for="recipient_province">Tỉnh / thành</label>
                                <select name="recipient_province" id="recipient_province">
                                    <option value="null" selected>Chọn tỉnh / thành</option>
                                    <option value="50">Hồ Chí Minh</option>
                                    <option value="1">Hà Nội</option>
                                    <option value="56">Đồng Tháp</option>
                                </select>
                            </div>

                            <div class="shipping__district">
                                <label class="shipping__label" for="recipient_district">Quận / huyện</label>
                                <select name="recipient_district" id="recipient_district">
                                    <option value="null" selected>Chọn quận / huyện</option>
                                    <option value="466">Quận 1</option>
                                    <option value="475">Quận 10</option>
                                    <option value="470">Quận 5</option>
                                </select>
                            </div>

                            <div class="shipping__ward">
                                <label class="shipping__label" for="recipient_ward">Phường / xã</label>
                                <select name="recipient_ward" id="recipient_ward">
                                    <option value="null" selected>Chọn phường / xã</option>
                                    <option value="26896">Phường 01</option>
                                    <option value="26884">Phường 10</option>
                                    <option value="26902">Phường 03</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="checkout__payment-method__box">
                <h2>Phương thức thanh toán</h2>
                <ul class="checkout__payment-methods">
                    <li class="payment-method__item">
                        <div class="payment-method__header">
                            <div class="payment-method__checkbox">
                                <input type="radio" name="payment_method" class="payment-radio" id="flexRadioDefault1">
                                <label for="flexRadioDefault1" class="payment-label">Chuyển khoản ngân hàng</label>
                            </div>
                        </div>
                        <div class="payment-method__content">
                            <div class="payment-method__body">
                                <p>Hãy chuyển khoản trực tiếp vào tài khoản ngân hàng của chúng tôi. Vui lòng sử dụng Mã đơn hàng của bạn làm nội dung chuyển khoản. Đơn hàng sẽ không được giao cho đến khi tiền được ghi nhận vào tài khoản của chúng tôi.
                                </p>
                            </div>
                        </div>
                    </li>
                    <li class="payment-method__item">
                        <div class="payment-method__checkbox">
                            <input class="form-check-input radio" type="radio" id="flexRadioDefault2" name="payment_method" checked>
                            <svg>
                                <use href="#icon-payment-cod"></use>
                            </svg>
                            <label class="form-check-label" for="flexRadioDefault2">Thanh toán khi nhận hàng (COD)</label>
                        </div>
                    </li>
                    <li class="payment-method__item">
                        <div class="payment-method__checkbox">
                            <input class="form-check-input radio" type="radio" id="flexRadioDefault3" name="payment_method">
                            <svg>
                                <use href="#icon-vn-pay"></use>
                            </svg>
                            <label class="form-check-label" for="flexRadioDefault3">Thẻ ATM/Visa/Master/JCB/QR Pay qua cổng VNPAY</label>
                        </div>
                    </li>
                    <li class="payment-method__item">
                        <div class="payment-method__checkbox">
                            <input class="form-check-input radio" type="radio" id="flexRadioDefault4" name="payment_method">
                            <svg>
                                <use href="#icon-momo"></use>
                            </svg>
                            <label class="form-check-label" for="flexRadioDefault4">Ví MoMo</label>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
}