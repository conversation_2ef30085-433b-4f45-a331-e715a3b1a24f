const for_you = (function () {
  new Swiper(".for-you-product__swiper", {
    loop: true,
    // autoplay: {
    //   delay: 3000,
    //   disableOnInteraction: true,
    // },
    navigation: {
      nextEl: ".for-you-product__next",
      prevEl: ".for-you-product__prev",
    },
    spaceBetween: calcSpaceBetweenFromVW(1.5),
    breakpoints: {
      768: {
        slidesPerView: 4,
      },
    },
  });
  function calcSpaceBetweenFromVW(em) {
    const fs = (window.innerWidth * 0.652) / 100;
    if (fs > 20) return 20;
    return em * fs;
  }
  return {
    init: function () {},
  };
})();

document.addEventListener("DOMContentLoaded", function () {
  for_you.init();
});
