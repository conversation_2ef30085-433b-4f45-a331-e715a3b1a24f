package home

import (
    "goweb/views/layouts"
    "goweb/views/home/<USER>"
    "github.com/networld-solution/gos/templates"
	"goweb/views/partials"
)

templ Index() {
    @layouts.Master(nil, &[]templ.Component{head()}, scriptSlot()) { 
        @components.SlideshowHome()       
        @partials.VouchersCpn()
        @partials.FlashsaleCpn()
        @components.CategoryHome()   
        @components.NewArrivals()
        @components.VideoHome()
        @components.FeaturedOfferForYou()
        @components.BestSellerHome()
        @components.QuickViewModal()
        // @components.PromotionModal()
    }
}

templ head(){
    <link rel="stylesheet" href={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.css")} />
	<link rel="stylesheet" href={templates.AssetURL("/static/css/quick-view-modal.css")}>
	<link rel="stylesheet" href={templates.AssetURL("/static/css/featured_offer.css")}>
	<link rel="stylesheet" href={templates.AssetURL("/static/css/category.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/css/promotion-modal.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/css/flashsale.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/css/slideshow.css")}>  
	<link rel="stylesheet" href={templates.AssetURL("/static/css/video.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/css/mega-voucher.css")}>
}

templ scriptSlot(){
    <script type="text/javascript" src={templates.AssetURL("/static/js/maybi-ui.js")}></script>
    <script type="text/javascript" src={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.js")}></script>
    <script type="text/javascript" src={templates.AssetURL("/static/js/flash-sale.js") }></script>
    <script type="text/javascript" src={templates.AssetURL("/static/js/banner-home.js") }></script>
    <script src="https://www.youtube.com/iframe_api"></script>
    <script src={templates.AssetURL("/static/js/customize_youtube_video.js")}></script>
    <script src={templates.AssetURL("/static/js/quick_view_modal.js")}></script>
}