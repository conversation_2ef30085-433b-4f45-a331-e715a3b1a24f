/* Blogs Background */
.blogs-bg {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 18.75em;
    position: relative;
    display: flex;
    align-items: center;
    padding: 5em 0;
    font-size: var(--10px);
}

.blogs-bg .container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 75em;
    margin: 0 auto;
    padding: 0 0.9375em;
}

.blogs-bg .content {
    margin-bottom: 1.875em;
    text-align: left;
    color: white;
}

.blogs-bg .title {
    font-size: 3.125em;
    font-weight: 700;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    color: white;
}

/* Blogs Breadcrumb */
.blogs-breadcrumb {
    background: var(--bg-color-body);
    padding: 1.25em 0;
    border-bottom: 1px solid #e9ecef;
    font-size: var(--10px);
}

.blogs-breadcrumb .list {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5em;
}

.blogs-breadcrumb .item {
    display: flex;
    align-items: center;
    font-size: 1.125em;
    color: var(--color-body);
    font-weight: 400;
}

.blogs-breadcrumb .item:not(:last-child)::after {
    content: '';
    width: 1em;
    height: 1em;
    margin-left: 0.5em;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236c757d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='9,18 15,12 9,6'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center;
    background-size: contain;
}

.blogs-breadcrumb .link {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition);
}

.blogs-breadcrumb .link:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 992px) {
    .blogs-bg {
        padding: 3em 0 2em;
        min-height: 15em;
        font-size: 1.25vw;
    }

    .blogs-bg .title {
        font-size: 2.5em;
    }

    .blogs-bg .container {
        padding: 0 0.625em;
    }

    .blogs-breadcrumb {
        font-size: 1.25vw;
    }
}

@media (max-width: 768px) {
    .blogs-bg {
        padding: 2.5em 0 1.25em;
        min-height: 12.5em;
        font-size: 2vw;
    }

    .blogs-bg .title {
        font-size: 1.75em;
    }

    .blogs-breadcrumb {
        font-size: 2vw;
        padding: 1em 0;
    }

    .blogs-breadcrumb .item {
        font-size: 1em;
    }
}

@media (max-width: 576px) {
    .blogs-bg {
        padding: 2em 0 1em;
        min-height: 10em;
        font-size: 2.5vw;
    }

    .blogs-bg .title {
        font-size: 1.5em;
    }

    .blogs-breadcrumb {
        font-size: 2.5vw;
        padding: 0.75em 0;
    }

    .blogs-breadcrumb .item {
        font-size: 0.9em;
    }

    .blogs-breadcrumb .item:not(:last-child)::after {
        width: 0.8em;
        height: 0.8em;
    }
}

/* Menu Sub */
.menubrc-wrapper {
    padding-bottom: 1.2em;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 1.5vw;
}

.menubrc {
    display: flex;
    gap: 2em;
    /* padding-bottom: 0.5em; */
    justify-content: center;
    font-size: 1vw;
}

.menubrc-item {
    text-decoration: none;
    color: #000;
    font-weight: 500;
    padding: 0.2em 1.6em;
    border-radius: 100px;
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.menubrc-item:hover {
    background: var(--primary);
    color: #fff;
}

.menubrc-item.active {
    background-color: var(--primary);
    color: #fff;
    font-weight: bold;
}

.menu-line-brc {
    height: 1px;
    background-color: rgba(208, 208, 208, 1);
    width: 80%;
    margin-top: 1.5vw;
}

.nav-btn {
    display: none;
}

/* Responsive */
@media (max-width: 768px) {
    .menubrc {
        gap: 3em;
        padding-bottom: 1em;
        font-size: 1.25vw;
    }

    .nav-btn {
        display: none;
    }
}

@media (max-width: 575px) {
    .menubrc {
        gap: 0.5em;
        padding-bottom: 1em;
        font-size: 2.5vw;
        flex-wrap: nowrap;
        min-width: max-content;
        padding: 0.8em 0.5em;
    }

    .menubrc-item {
        text-decoration: none;
        color: var(--neutral-100);
        font-weight: 500;
        padding: 0.6em 2;
        width: 45%;
        display: flex;
        justify-content: center;
        align-items: center;
        white-space: nowrap;
    }

    .menubrc-wrapper.container {
        overflow: hidden;
        position: relative;
        display: block;
    }

    .menubrc-con {
        display: flex;
        flex-direction: row;
    }

    .menubrc-scroll {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .menubrc-wrapper .menu-line {
        display: none;
    }

    .nav-btn {
        position: absolute;
        top: 33%;
        transform: translateY(-50%);
        background-color: #fff;
        border: 2px solid var(--primary);
        border-radius: 50%;
        width: 2.5em;
        height: 2.5em;
        font-size: 2.5vw;
        font-weight: bold;
        color: var(--primary);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        touch-action: manipulation;
    }

    .prev-btn {
        left: 0;
    }

    .next-btn {
        right: 0;
    }
}

/* Newest Section */
.newest-section {
    display: flex;
    flex-direction: column;
    gap: 1em;
    margin-top: 2em;
    font-size: 0.625vw;
}

.newest-section__title {
    font-size: 2.4em;
    font-weight: 700;
    color: var(--primary);
}

.newest-section__wrapper {
    width: 100%;
    height: fit-content;
    display: flex;
    gap: 1.5em;
}

.newest-section__left {
    position: relative;
    width: 60%;
}

.newest-section__bottom {
    width: 100%;
    position: absolute;
    display: flex;
    justify-content: space-between;
    align-items: end;
    padding: 3em 2em 1em 2em;
    bottom: 0;
    font-size: 1vw;
    gap: 1em;
    background: linear-gradient(to top, black, transparent);
    z-index: 1;
}

.newest-section__bottom-title {
    font-size: 1.6em;
    font-weight: 600;
    color: rgb(255, 255, 255);
}

.newest-section__bottom-meta {
    display: flex;
    align-items: center;
    font-size: 0.5em;
}

.bottom-meta_support {
    display: flex;
    gap: 2em;
}

.bottom-meta_support-item {
    font-size: 1.2em;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
    color: rgb(255 255 255);
}

.newest-section__bottom-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 1em 1em 0 0;
}

.newest-section__right {
    width: 40%;
    gap: 2em;
    display: flex;
    flex-direction: column;
}

.newest-right-item {
    display: flex;
    font-size: 0.625vw;
    border-radius: 1em 0 0 1em;
    overflow: hidden;
}

.newest-right-thumb {
    width: 33%;
    position: relative;
    aspect-ratio: 4/3;
}

.newest-right-item:hover .newest__thumb-img {
    transform: scale(1.05);
    transition: transform 0.3s linear;
}

.newest-right-thumb__link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.newest__thumb-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 1em 0 0 1em;
}

.newest-right-content {
    display: flex;
    flex-direction: column;
    font-size: 0.5vw;
    padding: 1em 2em;
    position: relative;
    width: 67%;
    justify-content: space-between;
}

.newest-right-content__title {
    font-size: 2em;
    font-weight: 500;
    line-height: 1.6em;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.newest-right__support-item .nitem__support-icon svg {
    width: 100%;
    height: 100%;
    object-fit: cover;
    fill: var(--color-body);
}

.newest-right__bottom {
    font-size: 1.3em;
}

.newest-right__support {
    display: flex;
    align-items: center;
    gap: 2em;
}

.newest-right__support-item {
    display: flex;
    align-items: center;
    gap: 0.3em;
}

.newest-right__support-item span {
    color: var(--color-body);
    font-style: italic;
}

.grid-wrapper {
    margin-top: 3em;
    font-size: 0.625vw;
}

.grid-wrapper .row-wrapper {
    font-size: 0.652vw;
    display: flex;
    gap: 1.5em;

}

.grid-wrapper .item-row {
    width: 33%;
}

.grid-wrapper .nitem__content--row {
    width: 50%;
}

.grid-wrapper-detail {
    padding: 2em;
    margin-top: 2em;
    width: 100%;
    height: 100%;
    border-radius: 1em;
}

.grid-wrapper__title {
    align-items: center;
    margin-bottom: 2em;
}

.grid__analytic {
    font-size: 2.4em;
    font-weight: 700;
    color: var(--primary);
}

.grid__support {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
    cursor: pointer;
    font-size: 1.3em;
}

.row-wrapper {
    display: flex;
    height: 100%;
    width: 100%;
    font-size: 0.652vw;
    justify-content: space-between;
}

.nitem:hover .nitem__thumb-img {
    transform: scale(1.05);
    transition: all 0.3s linear;
}

.nitem__thumb--row {
    width: 60%;
    padding: 1em;
    aspect-ratio: 4 / 3;
}

.grid-wrapper .nitem__thumb--row {
    width: 50%;
    border-top-left-radius: 1em;
    border-bottom-left-radius: 1em;
    overflow: hidden;
}

.grid-wrapper .nitem {
    flex: 1;
    display: flex;
    gap: 0.5em;
}

.nitem-row {
    display: flex;
    border-radius: 0 !important;
}

.nitem-row:after {
    content: "";
    display: block;
    width: 1px;
    background: #b5b4b4;
    height: 100%;
}

.nitem-row:last-child:after {
    display: none;
}

.nitem__thumb-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-top-left-radius: 1em;
    border-bottom-left-radius: 1em;
}

.nitem__content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.nitem__thumb--row,
.nitem__content--row {
    width: 17.5em;
    position: relative;
    padding: 1em;
    aspect-ratio: 4 / 3;
}

.nitem__thumb-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.nitem__title {
    font-size: 2em;
    font-weight: 500;
}

.nitem__title__grid {
    font-weight: 500;
    font-size: 1.5em;
    line-height: 1.7em;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis
}

.nitem-row__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9em;
}

.nitem-row__bottom .nitem__support-icon svg {
    fill: var(--color-body);
}

.nitem-row__support {
    display: flex;
    gap: 2em;
}

.nitem__support-item {
    font-size: 1em;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.3em;
    color: var(--color-body);
}

.nitem__support-item span {
    font-size: 1.2em;
    color: var(--txt-lightgray);
    font-style: italic;
}

.nitem__support-icon {
    width: 2em;
    aspect-ratio: 1;
}

.nitem__support-icon svg {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.vertical-line {
    width: 1px;
    height: 100%;
    background-color: #ddd;
}

/* Responsive */
@media (max-width: 768px) {
    .grid-wrapper {
        border-radius: 2em;
    }

    .grid-wrapper__title {
        margin-bottom: 0;
        background: linear-gradient(269.28deg, #009585 0%, #67CCC3 99.94%);
        text-align: center;
        font-size: 1.5vw;
        padding: 1em;
        border-radius: 1em 1em 0 0;
    }

    .grid__analytic {
        font-size: 1.6em;
        color: var(--neutral-900);
    }

    .grid-wrapper .row-wrapper {
        font-size: 1.5vw;
        display: flex;
        flex-direction: column;
        gap: 2vw;
        background: rgba(246, 248, 250, 1);
        border-radius: 0 0 1em 1em;
    }

    .grid-wrapper .nitem {
        width: 100%;
        flex: 1;
        flex-direction: row-reverse;
    }

    .grid-wrapper .nitem__thumb--row {
        width: 26%;
        padding: 0;
    }

    .grid-wrapper .nitem__content--row {
        width: 67%;
        aspect-ratio: unset;
        display: flex;
        justify-content: space-between;
    }

    .nitem__support-item span {
        color: #8d8a8a;
    }

    .grid-wrapper .nitem__thumb--row {
        border-radius: 1em;
    }

    .nitem-row {
        position: relative;
    }

    .nitem-row:after {
        position: absolute;
        content: "";
        display: block;
        height: 1px;
        bottom: -0.7em;
        background: #b5b4b4;
        width: 100%;
    }

    .nitem-row:last-child:after {
        display: none;
    }
}

@media (max-width: 575px) {
    .grid-wrapper__title {
        font-size: 2.25vw;
    }

    .grid-wrapper .row-wrapper {
        font-size: 2.25vw;
    }
}

@media (max-width: 768px) {
    .newest-section__title {
        font-size: 1.569rem;
    }

    .newest-section__bottom-meta {
        font-size: 0.8em;
    }

    .newest-section__bottom-title {
        font-size: 2.6em;
        font-weight: 600;
    }

    .newest-section__wrapper {
        width: 100%;
        height: fit-content;
        display: flex;
        flex-direction: column;
        gap: 1.5em;
    }

    .newest-section__left {
        position: relative;
        width: 100%;
    }

    .newest-section__right {
        width: 100%;
        gap: 1em;
        display: flex;
        flex-direction: row;
    }

    .newest-right-item {
        display: flex;
        flex-direction: column;
        width: 30%;
        border-radius: 1em;
    }

    .newest-right-thumb {
        width: 100%;
        position: relative;
        aspect-ratio: 16 / 9;
    }

    .newest-right-content {
        background-color: #f6f6f6;
        padding: 2em;
        gap: 1em;
        width: 100%;
    }

    .newest-right__bottom {
        font-size: 1.5em;
    }

    .newest-right-content__title {
        font-size: 3em;
        font-weight: 500;
        line-height: 1.6em;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .newest__thumb-img {
        border-radius: 1em 1em 0 0;
    }
}

@media (max-width: 575px) {
    .newest-section__title {
        font-size: 1.569rem;
    }

    .newest-section__right {
        flex-direction: row;
        overflow-x: auto;
        gap: 1vw;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
    }

    .newest-right-item {
        min-width: 50%;
        /* flex: 0 0 auto; */
        scroll-snap-align: start;
    }

    .newest-right-content {
        font-size: 1em;
        gap: 1em;
    }

    .newest-right-content__title {
        font-size: 3em;
    }

    .newest-right__bottom {
        font-size: 1.5em;
    }

    .bottom-meta_support-item img {
        width: 60%;
        height: 60%;
    }

    .bottom-meta_support-item {
        font-size: 1.8em;
        gap: 0px;
    }
}

/* Analytic Main */
.ana-main {
    width: 100%;
    margin-top: 4em;
}

.ana-main__wrapper {
    display: grid;
    grid-template-columns: auto 20vw;
    margin-bottom: 3em;
    gap: 3em;
}

.ana-row-grid {
    display: grid;
    height: 100%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 1em;
    justify-items: center;
    width: 100%;
}

.nitem {
    display: flex;
    border-radius: 1em;
}

.ana-grid-wrapper {
    font-size: 0.652vw;
    padding: 2em;
    background-color: rgba(246, 248, 250, 1);
    border-radius: 1em;
}

.ana-grid-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
    margin-right: 1em;
}

.ana-grid-box__title {
    font-size: 2.3em;
    font-weight: 700;
    color: var(--primary-500);
}

.ana-grid-box__seemore {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1em;
    cursor: pointer;
    font-size: 1.4em;
    font-weight: 700;
    color: #7c7c7c;
}

.ana--row {
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.ana__grid--row {
    border-radius: 1em;
    background-color: #ffffff;
    gap: 0em;
    border-top-left-radius: 1em;
    border-top-right-radius: 1em;
    overflow: hidden;
}

.ana__thumb--row,
.ana__content--row {
    width: 100% !important;
    aspect-ratio: 16 / 9;
}

.ana__thumb {
    width: 31.5%;
    position: relative;
    aspect-ratio: 4/3;
}

.ana__thumb-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-top-left-radius: 1em;
    border-bottom-left-radius: 1em;
}

.ana__thumb-img--row {
    border-top-left-radius: 1em;
    border-top-right-radius: 1em;
    border-bottom-left-radius: 0;
}

.ana__content--row {
    padding: 2em;
    gap: 1em;
}

.ana__content {
    display: flex;
    flex-direction: column;
    gap: 1.5em;
    padding: 1.5em;
    justify-content: space-between;
}

.ana__title {
    font-weight: 500;
    font-size: 1.6em;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 3.4em;
}

.ana__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ana__support {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2em;
}

.ana__support-item {
    font-size: 1em;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5em;
    color: rgb(var(--gray));
}

.ana__support-item span {
    font-size: 1em;
    line-height: normal;
    color: var(--txt-lightgray);
    font-style: italic;

}

/* Responsive */
@media (max-width: 768px) {
    .ana-grid-box__title {
        font-size: 1.569rem;
    }

    .ana-row-grid {
        gap: 1em;
        display: flex;
        flex-direction: column;
    }

    .ana__grid--row.ana--row {
        gap: 0em;
        display: flex;
        flex-direction: row;
    }

    .ana__thumb {
        width: 31.5% !important;
        position: relative;
        aspect-ratio: 4 / 3;
    }

    .ana__content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 68.5% !important;
        padding: 2em 2em 2em 3em;
    }

    .ana__content--row {
        aspect-ratio: unset;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
    }

    .ana-grid-wrapper {
        font-size: 1.25vw;
    }

    .ana__grid--row .ana__thumb-img--row {
        border-top-left-radius: 1em;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 1em;
    }

    .ana-grid-box__seemore {
        font-size: 1.5vw;
    }
}

@media (max-width: 575px) {
    .ana-grid-wrapper {
        font-size: 1.75vw;
    }
    .ana-grid-box__seemore {
        font-size: 2vw;
    }
}