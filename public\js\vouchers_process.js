const offcanvasVouchers = document.getElementById("offcanvas_voucher");
const vouchersPanel = offcanvasVouchers.querySelector(".sheet__panel");
const voucherContainer = document.querySelector(".voucher__list");
const vouchers = (function () {
    const openOffcanvasVouchers = () => {
        document.addEventListener("click", function (e) {
            const btn = e.target.closest(".btn-open-vouchers");
            if (!btn) return;
            offcanvasVouchers?.classList.add("is-open");
        });
    };
    const closeOffcanvasVouchers = () => {
        const closeBtn = offcanvasVouchers.querySelector(".sheet__close");
        closeBtn.addEventListener("click", () => {
            offcanvasVouchers.classList.remove("is-open");
        });

        offcanvasVouchers.addEventListener("click", (e) => {
            const isOutsidePanel = !vouchersPanel.contains(e.target);
            const wrapper = e.target.closest(".voucher-item-wrapper");

            if (wrapper && !wrapper.classList.contains("voucher-disabled")) {
                offcanvasVouchers.classList.remove("is-open");
                return;
            }

            // Nếu click ngoài panel
            if (isOutsidePanel) {
                offcanvasVouchers.classList.remove("is-open");
            }
        });
    };


    const handleVoucherSelectionChange = () => {
        voucherContainer.addEventListener("change", (e) => {
            const radio = e.target;
            if (!radio.matches('input[name="freeship_voucher"], input[name="discount_voucher"]')) return;
            const voucherId = radio.dataset.voucherId;
            const selectedVoucher = document.getElementById(voucherId);
            const titleText = selectedVoucher?.querySelector(".voucher-item__title")?.textContent?.trim();

            if (titleText !== "") {
                const type = radio.name === "freeship_voucher" ? "freeship" : "discount";
                const chips = document.querySelectorAll(".cart-vouchers__chip." + type);
                let visibleChip = null;

                for (let i = 0; i < chips.length; i++) {
                    if (chips[i].offsetParent !== null) {
                        visibleChip = chips[i];
                        break;
                    }
                }

                if (visibleChip) {
                    const span = visibleChip.querySelector("span");
                    if (span) {
                        span.textContent = titleText;
                    }
                }
            }
        });
    }

    return {
        init: function () {
            openOffcanvasVouchers();
            closeOffcanvasVouchers();
            handleVoucherSelectionChange();
        },
    };
})();

document.addEventListener("DOMContentLoaded", function (event) {
    vouchers.init();
});
