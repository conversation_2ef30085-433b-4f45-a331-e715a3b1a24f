
.modal__promotion > .modal {
    margin: 0;
    border-radius: 0;
    padding: 0;
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50em;
    height: 59em;
   
}

.modal__promotion > .modal > .promotion {
    width: 100%;
    height: 100%;
}

.modal__promotion .modal > .modal__close {
    top: -3em;
    right: -2em;
    width: 3em;
    height: 3em;
    border: none;
    background-color: #ccc;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal__promotion .modal > .modal__close > svg {
    fill: var(--white);
    width: 1.8em;
    height: 1.8em;
}


@media (max-width: 768px){
    .modal__promotion > .modal {
        width: 50rem;
        height: auto;
        max-width: 80%;
        transform: translateY(-50%);
    }
    .modal__promotion > .modal img {
        object-fit: contain;
    }

    .modal__promotion .modal > .modal__close {
        top: -3rem;
        right: -3rem;
        width: 3rem;
        height: 3rem;
    
    }
    .modal__promotion .modal > .modal__close > svg {
        width: 1.8rem;
        height: 1.8rem;
    }
}