package foryou

import(
    "goweb/views/layouts"
    "github.com/networld-solution/gos/templates"
    "goweb/views/foryou/components"
)

templ ForYou(){
    @layouts.Master(nil, &[]templ.Component{head()}, scriptSlot()) {
        @components.ProductBackground()
        <section class="list-product">
			<div class="container90">
				<div class="list-product__layout">
					<!-- Sidebar Backdrop -->
					<div class="list-product__sidebar-backdrop"></div>

					<!-- Sidebar Filter -->
					@components.SidebarFilter()

					<!-- Main Content Area : List Product -->
					@components.ListProductWithCategories()
				</div>
			</div>
		</section>
    }
}

templ head(){
    <link rel="stylesheet" href={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.css")} />
    <link rel="stylesheet" href={templates.AssetURL("/static/css/product.css")}>
    <link rel="stylesheet" href={ templates.AssetURL("/static/css/list-product.css") }/>
    <link rel="stylesheet" href={ templates.AssetURL("/static/css/for-you.css") }/>
}
templ scriptSlot(){
    <script type="text/javascript" src={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.js")}></script>
    <script src={ templates.AssetURL("/static/js/list-product.js") }></script>
     <script type="text/javascript" src={templates.AssetURL("/static/js/for-you.js")}></script>
}