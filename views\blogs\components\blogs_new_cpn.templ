package components

// Mock data structure for blog posts
type BlogPost struct {
Title string
PagePath string
Img string
Views string
Likes string
}

templ BlogsNewCpn() {
<section class="container90 newest-section">
    <span class="newest-section__title">BÀI MỚI NHẤT</span>
    <div class="newest-section__wrapper">
        @blogNewLeftCpn("Thời trang công sở 2025: S<PERSON> kết hợp hoàn hảo giữa thanh lịch và hiện đại",
        "/tin-tuc/thoi-trang-cong-so-2025.html", "/static/images/blogs/blog-office-main.webp", "4.200",
        "1.500")
        <div class="newest-section__right">
            @blogNewItemRightCpn("Xu hướng thời trang Xuân Hè 2025", "/tin-tuc/xu-huong-xuan-he-2025.html",
            "/static/images/blogs/blog-trend-spring.png", "2.500", "850")
            @blogNewItemRightCpn("5 Cách ph<PERSON>i đồ công sở thanh lịch",
            "/tin-tuc/phoi-do-cong-so-thanh-lich.html",
            "/static/images/blogs/blog-office-style.jpg", "1.800", "620")
            @blogNewItemRightCpn("Bộ sưu tập mới: Blooming Dreams",
            "/tin-tuc/bo-suu-tap-blooming-dreams.html",
            "/static/images/blogs/blog-collection-new.jpg", "3.200", "1.100")
            @blogNewItemRightCpn("Phong cách Minimalist cho phụ nữ hiện đại",
            "/tin-tuc/phong-cach-minimalist.html",
            "/static/images/blogs/blog-minimalist.jpg", "1.950", "720")
        </div>
    </div>
</section>
}

templ blogNewLeftCpn(title, pagePath, img, views, likes string){
<div class="newest-section__left">
    <div class="newest-section__bottom">
        <h3 class="newest-section__bottom-title">
            <a href={templ.SafeURL(pagePath)} title={title}>{title}</a>
        </h3>
        <div class="newest-section__bottom-meta">
            <ul class="bottom-meta_support">
                <li class="bottom-meta_support-item">
                    <div class="nitem__support-icon">
                        <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                            <use xlink:href="#icon-like-blog"></use>
                        </svg>
                    </div>
                    <span>{likes}</span>
                </li>
                <li class="bottom-meta_support-item">
                    <div class="nitem__support-icon">
                        <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                            <use xlink:href="#icon-eye-blog"></use>
                        </svg>
                    </div>
                    <span>{views}</span>
                </li>
            </ul>
        </div>
    </div>
    <a href={templ.SafeURL(pagePath)} title={title} class="newest-section__bottom-thumb">
        <img class="newest-section__bottom-img" src={img} alt={title}>
    </a>
</div>
}

templ blogNewItemRightCpn(title, pagePath, img, views, likes string) {
<article class="newest-right-item hover-gray">
    <div class="newest-right-thumb">
        <a href={templ.SafeURL(pagePath)} title={title} class="newest-right-thumb__link">
            <img class="newest__thumb-img" src={img} alt={title}>
        </a>
    </div>
    <div class="newest-right-content">
        <h3 class="newest-right-content__title">
            <a class="txt-hover-green" href={templ.SafeURL(pagePath)} title={title}>{title}</a>
        </h3>
        <div class="newest-right__bottom">
            <ul class="newest-right__support">
                <li class="newest-right__support-item">
                    <div class="nitem__support-icon">
                        <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                            <use xlink:href="#icon-like-blog"></use>
                        </svg>
                    </div>
                    <span>{likes}</span>
                </li>
                <li class="newest-right__support-item">
                    <div class="nitem__support-icon">
                        <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                            <use xlink:href="#icon-eye-blog"></use>
                        </svg>
                    </div>
                    <span>{views}</span>
                </li>
            </ul>
        </div>
    </div>
</article>
}