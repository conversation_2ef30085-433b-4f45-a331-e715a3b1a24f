package components

import (
)

templ VideoHome() {
<section class="video-section">
    <div class="bg-parallax" style="
        background-image: url('/static/images/videobg.jpg');
        background-position-y: 0px;">
        <div class="video-container">
            <div class="video__wrapper">
                <div class="video__play">
                    <div id="play-toggle-btn" class="video__play_btn">
                        <svg class="video__emoji">
                            <use href="#icon-badge-emoji"></use>
                        </svg>
                    </div>
                    <div class="video__content border-white">
                        <span class="text__char" style="--char-rotate: 0deg">m</span>
                        <span class="text__char" style="--char-rotate: 13.333333333333334deg">a</span>
                        <span class="text__char" style="--char-rotate: 26.666666666666668deg">y</span>
                        <span class="text__char" style="--char-rotate: 40deg">b</span>
                        <span class="text__char" style="--char-rotate: 53.333333333333336deg">i</span>
                        <span class="text__char" style="--char-rotate: 66.66666666666667deg">-</span>
                        <span class="text__char" style="--char-rotate: 80deg"> </span>
                        <span class="text__char" style="--char-rotate: 93.33333333333334deg">m</span>
                        <span class="text__char" style="--char-rotate: 106.66666666666667deg">a</span>
                        <span class="text__char" style="--char-rotate: 120deg">y</span>
                        <span class="text__char" style="--char-rotate: 133.33333333333334deg">b</span>
                        <span class="text__char" style="--char-rotate: 146.66666666666669deg">i
                        </span>
                        <span class="text__char" style="--char-rotate: 160deg">-</span>
                        <span class="text__char" style="--char-rotate: 173.33333333333334deg"></span>
                        <span class="text__char" style="--char-rotate: 186.66666666666669deg">m</span>
                        <span class="text__char" style="--char-rotate: 200deg">a</span>
                        <span class="text__char" style="--char-rotate: 213.33333333333334deg">y</span>
                        <span class="text__char" style="--char-rotate: 226.66666666666669deg">b</span>
                        <span class="text__char" style="--char-rotate: 240deg">i</span>
                        <span class="text__char" style="--char-rotate: 253.33333333333334deg">-</span>
                        <span class="text__char" style="--char-rotate: 266.6666666666667deg">m
                        </span>
                        <span class="text__char" style="--char-rotate: 280deg">a</span>
                        <span class="text__char" style="--char-rotate: 293.33333333333337deg">y</span>
                        <span class="text__char" style="--char-rotate: 306.6666666666667deg">b</span>
                        <span class="text__char" style="--char-rotate: 320deg">i</span>
                        <span class="text__char" style="--char-rotate: 333.33333333333337deg"></span>
                        <span class="text__char" style="--char-rotate: 346.6666666666667deg">-</span>
                    </div>
                </div>
                <div class="youtube-video__wrapper" id="player-container">
                    <div id="player"></div>
                </div>

            </div>
        </div>
    </div>

    <div class="features__wrapper">
        <ul class="features">
            <li class="feature__item">
                <h2>Đầm</h2>
            </li>
            <li class="feature__item">
                <svg>
                    <use href="#icon-star"></use>
                </svg>
            </li>
            <li class="feature__item">
                <h2>Áo</h2>
            </li>
            <li class="feature__item">
                <svg>
                    <use href="#icon-star"></use>
                </svg>
            </li>
            <li class="feature__item">
                <h2>Quần</h2>
            </li>
            <li class="feature__item">
                <svg>
                    <use href="#icon-star"></use>
                </svg>
            </li>
            <li class="feature__item">
                <h2>Chân váy</h2>
            </li>
            <li class="feature__item">
                <svg>
                    <use href="#icon-star"></use>
                </svg>
            </li>
            <li class="feature__item">
                <h2>Áo dài</h2>
            </li>
            <li class="feature__item">
                <svg>
                    <use href="#icon-star"></use>
                </svg>
            </li>
            <li class="feature__item">
                <h2>Quần áo dài</h2>
            </li>
            <li class="feature__item">
                <svg>
                    <use href="#icon-star"></use>
                </svg>
            </li>
            <li class="feature__item">
                <h2>Đồ ngủ</h2>
            </li>
            <li class="feature__item">
                <svg>
                    <use href="#icon-star"></use>
                </svg>
            </li>
            <li class="feature__item">
                <h2>Áo chống nắng</h2>
            </li>
            <li class="feature__item">
                <svg>
                    <use href="#icon-star"></use>
                </svg>
            </li>
            <li class="feature__item">
                <h2>Set Jumpsuit</h2>
            </li>
            <li class="feature__item">
                <svg>
                    <use href="#icon-star"></use>
                </svg>
            </li>
            <li class="feature__item">
                <h2>Phụ kiện</h2>
            </li>
            <li class="feature__item">
                <svg>
                    <use href="#icon-star"></use>
                </svg>
            </li>
        </ul>
    </div>
</section>
}