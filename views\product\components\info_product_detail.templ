package components

import (
    "github.com/networld-solution/gos/templates"
)

templ InfoProductDetailCpn(){
    <div class="desc-rate__wrapper">
        <ul class="nav-tabs nav-product-detail" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" role="tab" id="tab-description" aria-selected="true" aria-controls="panel-description" tabindex="0"><PERSON><PERSON> tả</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" role="tab" id="tab-reviews" aria-selected="false" aria-controls="panel-reviews" tabindex="-1">Đ<PERSON>h giá (3)</button>
                
            </li>
        </ul>

        <div class="tab-panes">
            @tabDescription()
            @tabReviews()
        </div>
    </div>
}
templ tabDescription() {
    <div class="tab-pane active" id="panel-description" data-tab="description" role="tabpanel" aria-labelledby="tab-description">
        <div class="desc__box">
            <div class="desc__box-center">
                <div class="desc-center__top">
                    <h2>Mô tả sản phẩm</h2>
                    <div class="desc__content">
                        <div class="desc__item">
                            <strong>Tên sản phẩm:</strong>
                            <p>Áo kiểu nữ đắp tà lụa </p>
                        </div>
                        <div class="desc__item">
                            <strong>Mã sản phẩm:</strong> 
                            <p>2502R1WT29</p>
                        </div>
                        <div class="desc__item">
                            <strong>Chất liệu:</strong> 
                            <p>Lụa Dony</p>
                        </div>
                        <div class="desc__item">
                            <strong>Mô tả chất liệu:</strong> 
                            <p>Mỏng nhẹ, mềm, thoáng mát</p>
                        </div>
                        <div class="desc__item">
                            <strong>Mô tả kiểu dáng:</strong> 
                            <p>Áo dáng chiết eo nhẹ, đắp tà cột ngang eo</p>
                        </div>
                        <div class="desc__item">
                            <strong>Công dụng sản phẩm:</strong>
                            <p>Phần tay được thiết kế khéo léo che phần vai, bắp tay to. Cổ V tạo cảm giác thân trên thon gọn hơn</p>
                        </div>
                    </div>
                    <div class="desc__content-video">
                        <iframe width="560" height="315"
                            src="https://www.youtube-nocookie.com/embed/TXgYvFwMqg0?rel=0"
                            title="YouTube video player"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen
                            referrerpolicy="strict-origin-when-cross-origin">
                        </iframe>
                    </div>
                </div>
                <div class="desc-right__center">
                    <a class="desc-right__center-item" href="#" title="100%">
                        <h4>100% lụa</h4>
                        <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
                    </a>
                    <a class="desc-right__center-item" href="#" title="100%">
                        <h4>100% lụa</h4>
                        <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
                    </a>
                    <a class="desc-right__center-item" href="#" title="100%">
                        <h4>100% lụa</h4>
                        <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
                    </a>
                    <a class="desc-right__center-item" href="#" title="100%">
                        <h4>100% lụa</h4>
                        <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
                    </a>
                </div>
                <div class="desc-right__bot">
                    <div class="desc-right__bot-thumb">
                        <img class="product__img" src={templates.AssetURL("/static/images/blazer-black-2.jpg")} alt="Sale Up to 50% Off" />
                    </div>
                </div>
            </div>
        </div>
    </div>
}
templ tabReviews() {
    <div class="tab-pane" id="panel-reviews" data-tab="reviews" role="tabpanel" aria-labelledby="tab-reviews" hidden>
        <div class="review__box">
            <div class="review__comment-area">
                <h2 class="review__comment-title">Bình luận</h2>
                <p class="review__comment-area_text">Dưới đây là những đánh giá từ khách hàng:</p>
                <ul class="comment-list">
                    <li class="comment">
                        <div class="comment-body">
                            <div class="comment__avatar-box">
                                <div class="comment__avatar-thumb">
                                    <img class="avatar" src={templates.AssetURL("/static/images/blazer-black-2.jpg")}>
                                </div>
                            </div>
                            <div class="comment-content">
                                <span class="fn">She-EO</span>
                                <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                                    industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type
                                    and scrambled it to make a type specimen book.</p>
                            </div>
                            <div class="reply">
                                <a rel="nofollow" class="comment-reply-link" href="#">Trả lời</a>
                            </div>
                        </div>
                        <ul class="children">
                            <li class="comment">
                                <div class="comment-body">
                                    <div class="comment__avatar-box">
                                        <div class="comment__avatar-thumb">
                                            <img class="avatar" src={templates.AssetURL("/static/images/dam-om-thun.webp")}>
                                        </div>
                                    </div>
                                    <div class="comment-content">
                                        <span class="fn">Aurora</span>
                                        <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                                            industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type
                                            and scrambled it to make a type specimen book.</p>
                                    </div>
                                    <div class="reply">
                                        <a rel="nofollow" class="comment-reply-link" href="#">Trả lời</a>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </li>
                    <li class="comment">
                        <div class="comment-body">
                            <div class="comment__avatar-box">
                                <div class="comment__avatar-thumb">
                                    <img class="avatar" src={templates.AssetURL("/static/images/damxoe1.webp")}>
                                </div>
                            </div>
                            <div class="comment-content">
                                <span class="fn">Sophia</span>
                                <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
                                    industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type
                                    and scrambled it to make a type specimen book.</p>
                            </div>
                            <div class="reply">
                                <a rel="nofollow" class="comment-reply-link" href="#">Trả lời</a>
                            </div>
                        </div>
                    </li>
                </ul>
                <div class="comment-respond">
                    <h3 class="comment-reply-title">Đánh giá của bạn</h3>
                    <p class="comment-respond__text">Hãy để lại đánh giá hoặc nhận xét về sản phẩm.</p>
                    <div class="clearfix">
                        <form id="comments_form" class="comment-form" enctype="multipart/form-data">
                            
                            <div class="comment-form__rating">
                                <span class="product-rating__title">Chất lượng sản phẩm (theo sao)</span>
                                <div class="mb-rating star-rating" data-rating="5">
                                    <svg class="mb-star" data-index="1"><use href="#icon-star-fill"></use></svg>
                                    <svg class="mb-star" data-index="2"><use href="#icon-star-fill"></use></svg>
                                    <svg class="mb-star" data-index="3"><use href="#icon-star-fill"></use></svg>
                                    <svg class="mb-star" data-index="4"><use href="#icon-star-fill"></use></svg>
                                    <svg class="mb-star" data-index="5"><use href="#icon-star-fill"></use></svg>
                                    <input type="hidden" name="rating" class="star-rating-value" value="5">
                                </div>
                            </div>
                            <div class="comment-form__author-phone">
                                <div class="comment-form__author">
                                    <input id="comment_name" placeholder="Tên của bạn" type="text" value="" name="comment_name">
                                </div>
                                <div class="comment-form__phone">
                                    <input id="comment_phone" placeholder="Số điện thoại" type="tel" value="" name="comment_phone">
                                </div>
                            </div>
                            <div class="comment-form__text">
                                <textarea id="comment_text" placeholder="Nội dung đánh giá" class="form-control4" name="comment_text" cols="45" rows="3" required=""></textarea>
                            </div>
                            <div class="comment-form__preview" id="file_preview"></div>
                            <div class="comment-form__file">
                                <input type="file" id="upload_image" name="images" accept="image/*" multiple hidden>
                                <label for="upload_image" class="comment-file__label">Tải ảnh</label>

                                <input type="file" id="upload_video" name="videos" accept="video/*" multiple hidden>
                                <label for="upload_video" class="comment-file__label">Tải video</label>
                            </div>
                            <div class="comment-form__submit">
                                <button id="submit" type="submit" class="btn btn-secondary">Gửi ngay</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
}