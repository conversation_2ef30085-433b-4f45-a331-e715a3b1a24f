.category{
  font-size: var(--10px);
  padding: 4em;
}

.categories{
  font-size: var(--10px);
  display: grid;
  justify-content: center;
  grid-template-columns: repeat(5, 19.6em);
  gap: 2.4em;
}

.category__item{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.6em;
  transition: transform 0.3s ease;
}

.category__item:hover .category__thumb{
  transform: scale(1.05);
}

.category__item:hover .category__icon{
  transform: scale(1.1);
}

.category__item:hover .category__title{
  background-position: 0;
  font-weight: 700;
}

.category__item:hover .category__title::before{
  width: 100%;
}

.category__thumb{
  border: 0.1em solid var(--base-border);
  background-color: rgb(var(--rgb-white));
  width: 10em;
  height: 10em;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.category__icon{
  width: 5em;
  height: 5em;
  transition: fill 0.3s ease, transform 0.3s ease;
}
.category__title{
  position: relative;
  display: inline-block;
  font-size: var(--10px);
  font-weight: 500;
  text-transform: capitalize;
  color: #000;
  overflow: hidden;
  z-index: 1;
  padding: 0.2em;
}

.category__title::after{
  content: '';
  position: absolute;
  top: 0;
  left: -75%;
  width: 50%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  transform: skewX(-20deg);
  pointer-events: none;
  z-index: 2;
  transition: left 0.6s ease-in-out;
}

.category__item:hover .category__title::after{
  left: 130%;
}
