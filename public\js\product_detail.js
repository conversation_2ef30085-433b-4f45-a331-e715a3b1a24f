const product_detail = (function () {
  const selectedFiles = [];
  const selectedFileKeys = new Set();
  const maxTotal = 6;
  const previewContainer = document.getElementById("file_preview");
  let idCounter = 0;

  const swiperProductDetail = new Swiper(".swiper-product-detail-img-main", {
    slidesPerView: 1,
    spaceBetween: calcSpaceBetweenFromVW(1.5),
    speed: 0,
    preloadImages: true,
    lazy: false,
  });
  new Swiper(".related-product__swiper", {
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: true,
    },
    navigation: {
      nextEl: ".related-product__next",
      prevEl: ".related-product__prev",
    },
    spaceBetween: calcSpaceBetweenFromVW(1.5),
    breakpoints: {
      0: {
        slidesPerView: 2,
      },
      769: {
        slidesPerView: 5,
      },
    },
  });
  function calcSpaceBetweenFromVW(em) {
    const fs = (window.innerWidth * 0.652) / 100;
    if (fs > 20) return 20;
    return em * fs;
  }
  document
    .querySelectorAll(".product-thumbnails >img")
    .forEach((thumb, index) => {
      thumb.addEventListener("click", () => {
        swiperProductDetail.slideTo(index);
        // Toggle active class
        document
          .querySelectorAll(".product-thumbnails >img")
          .forEach((img) => img.classList.remove("active"));
        thumb.classList.add("active");
      });
    });

  // Optional: set active on initial load
  document.querySelectorAll(".comma-separated").forEach((container) => {
    const paragraphs = container.querySelectorAll("p");

    paragraphs.forEach((p) => {
      const items = p.querySelectorAll(".comma-item");
      items.forEach((item, index) => {
        const next = item.nextSibling;
        if (
          next &&
          next.nodeType === Node.TEXT_NODE &&
          next.textContent.trim() === ","
        ) {
          next.remove();
        }
        if (index < items.length - 1) {
          item.insertAdjacentText("afterend", ", ");
        }
      });
    });
  });

  const initOffcanvasSupport = () => {
    const offcanvas = document.getElementById("product-offcanvas");
    if (!offcanvas) return;

    const content = document.getElementById("offcanvas-content");
    const overlay = offcanvas.querySelector(".offcanvas__overlay");
    const closeBtn = offcanvas.querySelector(".offcanvas__close");

    const showOffcanvasFromTemplate = (templateId) => {
      const template = document.getElementById(templateId);
      if (!template) return;

      content.innerHTML = template.innerHTML;
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
      document.body.style.paddingRight = `${scrollbarWidth}px`;
      offcanvas.classList.add("active");
      document.body.classList.add("offcanvas-open");
    };

    const hideOffcanvas = () => {
      offcanvas.classList.remove("active");
      content.innerHTML = "";
      document.body.style.paddingRight = '';
      document.body.classList.remove("offcanvas-open");
    };

    document.querySelector(".product-detail__return-policy")?.addEventListener("click", () => {
      showOffcanvasFromTemplate("offcanvas-content-return");
    });

    document.querySelector(".product-detail__size-guide")?.addEventListener("click", () => {
      showOffcanvasFromTemplate("offcanvas-content-size");
    });

    overlay?.addEventListener("click", hideOffcanvas);
    closeBtn?.addEventListener("click", hideOffcanvas);
  };

  const changePruductColor = () => {
    document.querySelectorAll(".product").forEach((product) => {
      product.addEventListener("click", (e) => {
        const color = e.target.closest(".product-color");
        if (!color || color.classList.contains("product-color--more")) return;

        const img = color.getAttribute("data-img");
        const productWrapper = color.closest(".product");
        const activeSlide = productWrapper.querySelector(
          ".swiper-slide-active"
        );
        let mainImg = "";
        if (activeSlide) {
          mainImg = activeSlide.querySelector(".product__img");
        } else {
          mainImg = productWrapper.querySelector(".product__img");
        }
        const allColors = [
          ...productWrapper.querySelectorAll(".product-color"),
        ].filter((el) => el.closest(".product") === productWrapper);
        allColors.forEach((c) => c.classList.remove("active"));
        color.classList.add("active");
        if (mainImg && img) {
          mainImg.setAttribute("src", img);
        }
      });
    });
  };

  //process upload img/video
  function bytesToMB(bytes) {
    return bytes / (1024 * 1024);
  }
  function createFileId() {
    return `file-${idCounter++}`;
  }
  function addFiles(newFiles) {
    const imageCount = selectedFiles.filter((f) =>
      f.file.type.startsWith("image/")
    ).length;
    const videoCount = selectedFiles.filter((f) =>
      f.file.type.startsWith("video/")
    ).length;

    let newImageCount = 0;
    let newVideoCount = 0;
    let hasNewFile = false;
    let duplicateFiles = [];
    let errorMessages = [];
    const validFiles = [];

    for (let i = 0; i < newFiles.length; i++) {
      const file = newFiles[i];
      const fileKey = file.name + "_" + file.lastModified;

      if (selectedFileKeys.has(fileKey)) {
        duplicateFiles.push(file.name);
        continue;
      }

      const isImage = file.type.startsWith("image/");
      const isVideo = file.type.startsWith("video/");

      if (isVideo) {
        if (bytesToMB(file.size) > 60) {
          errorMessages.push(`Video "${file.name}" vượt quá giới hạn 60MB.`);
          continue;
        }
        if (videoCount + newVideoCount >= 1) {
          errorMessages.push(`Đã đủ 1 video. "${file.name}" bị bỏ qua.`);
          continue;
        }
        newVideoCount++;
      }

      if (isImage) {
        if (imageCount + newImageCount >= 5) {
          errorMessages.push("Chỉ được phép tải tối đa 5 ảnh.");
          break;
        }
        newImageCount++;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) {
      if (duplicateFiles.length > 0) {
        errorMessages.push(
          `Các tệp đã chọn trước đó và bị bỏ qua:\n- ${duplicateFiles.join("\n- ")}`
        );
      }
      if (errorMessages.length > 0) {
        alert(errorMessages.join("\n"));
      }
      return;
    }

    // Kiểm tra tổng giới hạn file
    const totalCount = selectedFiles.length + validFiles.length;
    if (totalCount > 6) {
      errorMessages.push("Tổng cộng chỉ được phép tải tối đa 6 tệp (5 ảnh + 1 video).");
      if (duplicateFiles.length > 0) {
        errorMessages.push(
          `Các tệp đã chọn trước đó và bị bỏ qua:\n- ${duplicateFiles.join("\n- ")}`
        );
      }
      alert(errorMessages.join("\n"));
      return;
    }

    // Thêm vào danh sách
    for (const file of validFiles) {
      const fileKey = file.name + "_" + file.lastModified;
      const id = createFileId();
      selectedFiles.push({ file, id, fileKey });
      selectedFileKeys.add(fileKey);
      hasNewFile = true;
    }

    if (duplicateFiles.length > 0) {
      errorMessages.push(
        `Các tệp đã chọn trước đó và bị bỏ qua:\n- ${duplicateFiles.join("\n- ")}`
      );
    }

    if (errorMessages.length > 0) {
      alert(errorMessages.join("\n"));
    }

    if (hasNewFile) {
      renderAllPreviews();
    }
  }

  function renderAllPreviews() {
    previewContainer.innerHTML = "";
    const images = selectedFiles.filter((f) =>
      f.file.type.startsWith("image/")
    );
    const videos = selectedFiles.filter((f) =>
      f.file.type.startsWith("video/")
    );

    [...images, ...videos].forEach(({ file, id }) => {
      renderSinglePreview(
        file,
        id,
        file.type.startsWith("image/") ? "image" : "video"
      );
    });
  }
  function renderSinglePreview(file, id, type) {
    const reader = new FileReader();

    reader.onload = function (e) {
      let el;
      if (type === "image") {
        el = document.createElement("img");
        el.classList.add("preview-thumb");
        el.src = e.target.result;
      } else if (type === "video") {
        el = document.createElement("video");
        el.classList.add("preview-thumb");
        el.src = e.target.result;
        el.controls = true;
      }
      const wrapper = document.createElement("div");
      wrapper.classList.add("preview-wrapper");
      wrapper.setAttribute("data-id", id);
      const removeBtn = document.createElement("button");
      removeBtn.innerHTML =
        '<svg class="cart-icon__delete"><use href="#icon-close"></use></svg>';
      removeBtn.classList.add("preview-remove");
      removeBtn.addEventListener("click", () => {
        const index = selectedFiles.findIndex((f) => f.id === id);
        if (index !== -1) {
          const fileKey = selectedFiles[index].fileKey;
          selectedFiles.splice(index, 1);
          selectedFileKeys.delete(fileKey);
        }
        wrapper.remove();
      });
      wrapper.appendChild(el);
      wrapper.appendChild(removeBtn);
      previewContainer.appendChild(wrapper);
    };
    reader.readAsDataURL(file);
  }
  function handleImageInput(e) {
    addFiles(e.target.files, "image");
    e.target.value = "";
  }
  function handleVideoInput(e) {
    addFiles(e.target.files, "video");
    e.target.value = "";
  }
  function initCommentFileUploadPreview() {
    const imageInput = document.getElementById("upload_image");
    const videoInput = document.getElementById("upload_video");

    if (imageInput) {
      imageInput.addEventListener("change", handleImageInput);
    }

    if (videoInput) {
      videoInput.addEventListener("change", handleVideoInput);
    }
  }
  initCommentFileUploadPreview();

  function syncHeights() {
    const left = document.querySelector(".product-detail__content-left");
    const right = document.querySelector(".product-detail__content-right");
    if (!left || !right) return;
    if (document.documentElement.clientWidth <= 768) {
      left.style.minHeight = "0";
      return;
    }
    left.style.minHeight = "0";
    const rightHeight = right.getBoundingClientRect().height;
    const rightWidth = right.getBoundingClientRect().width;
    const paddingBottom = rightWidth * 0.05;

    const reducedHeight = Math.ceil(rightHeight - paddingBottom);
    left.style.minHeight = `${reducedHeight}px`;
  }

  function initStickyHeightSync() {
    syncHeights();
    window.addEventListener("resize", syncHeights);
    const rightEl = document.querySelector(".product-detail__content-right");
    if (rightEl) {
      const observer = new MutationObserver(syncHeights);
      observer.observe(rightEl, { childList: true, subtree: true });
    }
  }

  const initTabSwitcher = () => {
    const tabButtons = document.querySelectorAll(
      ".nav-product-detail .nav-link"
    );
    const tabPanes = document.querySelectorAll(".tab-pane");

    tabButtons.forEach((btn, index) => {
      btn.addEventListener("click", () => {
        tabButtons.forEach((b) => {
          b.classList.remove("active");
          b.setAttribute("aria-selected", "false");
          b.setAttribute("tabindex", "-1");
        });

        tabPanes.forEach((pane) => {
          pane.classList.remove("active");
          pane.setAttribute("hidden", "true");
        });

        btn.classList.add("active");
        btn.setAttribute("aria-selected", "true");
        btn.setAttribute("tabindex", "0");

        const target = index === 0 ? "description" : "reviews";
        const targetPane = document.querySelector(
          `.tab-pane[data-tab="${target}"]`
        );

        if (targetPane) {
          targetPane.classList.add("active");
          targetPane.removeAttribute("hidden");
        }
        syncHeights();
      });
    });
  };

  const initRatingClick = () => {
    const ratingElement = document.querySelector(".product-rating");
    const tabButtons = document.querySelectorAll(
      ".nav-product-detail .nav-link"
    );
    const tabPanes = document.querySelectorAll(".tab-pane");
    const tabWrapper = document.querySelector(".nav-tabs.nav-product-detail");

    if (!ratingElement || tabButtons.length < 2 || !tabWrapper) return;

    ratingElement.addEventListener("click", () => {
      tabWrapper.scrollIntoView({ behavior: "smooth", block: "start" });

      tabButtons.forEach((b) => b.classList.remove("active"));
      tabPanes.forEach((p) => p.classList.remove("active"));

      tabButtons[1].classList.add("active");
      const reviewPane = document.querySelector(
        '.tab-pane[data-tab="reviews"]'
      );
      reviewPane?.classList.add("active");

      syncHeights();
    });
  };

  // Helper function to format price
  const formatPrice = (price) => {
    const numPrice = parseInt(price);
    return numPrice.toLocaleString('vi-VN');
  };

  // Add gift product to cart function
  const addGiftProductToCart = (selectedSize) => {

    // Get gift product data from DOM
    const giftContentElement = document.querySelector('.gift-product__content');
    if (!giftContentElement) {
      return;
    }

    // Extract data from data attributes with error handling
    try {
      const availableSizes = JSON.parse(giftContentElement.dataset.giftSizes || '[]');
      const availableColors = JSON.parse(giftContentElement.dataset.giftColors || '[]');

      // Create gift product object with cart-compatible structure
      const giftProduct = {
        id: giftContentElement.dataset.giftId || 'gift-default',
        product_id: giftContentElement.dataset.giftId || 'gift-default',
        name: giftContentElement.dataset.giftName || 'Gift Product',
        image: giftContentElement.dataset.giftImage || '',
        price: (giftContentElement.dataset.giftPrice || '0') + (giftContentElement.dataset.giftCurrency || 'đ'),
        originalPrice: formatPrice(giftContentElement.dataset.giftOriginalPrice || '0') + (giftContentElement.dataset.giftCurrency || 'đ'),
        condition: giftContentElement.dataset.giftCondition || '',
        sku: giftContentElement.dataset.giftSku || '',
        quantity: 1,
        isGift: true,
        selectedSize: selectedSize,
        selectedColor: availableColors[0]?.color || '#000000',
        availableSizes: availableSizes,
        availableColors: availableColors,
        sizes: availableSizes.map(size => ({
          text: size,
          isActive: selectedSize === size,
          dataColor: JSON.stringify(availableColors)
        })),
        colors: availableColors.map((color, index) => ({
          backgroundColor: color.color || '#000000',
          isActive: index === 0,
          dataImg: color.img || ''
        }))
      };

      // Check if cart containers exist
      const cartSidebarContainer = document.querySelector('#cartSidebar .cart__list');
      const mainCartContainer = document.querySelector('.products-cart');

      if (!cartSidebarContainer && !mainCartContainer) {
        return;
      }

      // Use cartUpdater from master.templ
      if (typeof cartUpdater !== 'undefined' && cartUpdater.addGiftProductToCart) {
        cartUpdater.addGiftProductToCart(giftProduct);
      }
    } catch (error) {
      console.error('Error adding gift product to cart:', error);
      if (typeof cartUpdater !== 'undefined' && cartUpdater.showGiftAddedMessage) {
        cartUpdater.showGiftAddedMessage('Có lỗi xảy ra khi thêm sản phẩm tặng!');
      }
    }
  };





  // Populate modal with gift data from DOM
  const populateModalWithGiftData = () => {
    const giftContentElement = document.querySelector('.gift-product__content');
    if (!giftContentElement) return;

    try {
      // Update modal elements with gift data
      const modalImage = document.getElementById('giftModalImage');
      const modalTitle = document.getElementById('giftModalTitle');
      const modalDescription = document.getElementById('giftModalDescription');

      if (modalImage) {
        modalImage.src = giftContentElement.dataset.giftImage || '';
        modalImage.alt = giftContentElement.dataset.giftName || 'Gift Product';
      }

      if (modalTitle) {
        modalTitle.textContent = giftContentElement.dataset.giftName || 'Gift Product';
      }

      if (modalDescription) {
        modalDescription.textContent = giftContentElement.dataset.giftCondition || '';
      }

      // Update modal title
      const modalTitleElement = document.querySelector('.gift-modal__title');
      if (modalTitleElement) {
        modalTitleElement.textContent = `Chọn size cho ${giftContentElement.dataset.giftName || 'sản phẩm tặng'}`;
      }
    } catch (error) {
      console.error('Error populating modal with gift data:', error);
    }
  };



  const initGiftSizeModal = () => {
    const giftSizeItems = document.querySelectorAll('.gift-size-list .product-size__item');
    const confirmBtn = document.getElementById('confirmGiftSize');
    const cancelBtn = document.getElementById('cancelGiftSize');
    const closeBtn = document.getElementById('closeGiftSizeModal');
    const openBtn = document.getElementById('openGiftSizeModal');
    const sizeBtn = document.querySelector('.gift-product__size-btn span');
    const modalElement = document.getElementById('giftSizeModal');
    let selectedSize = null;

    if (!modalElement) return;

    // Open modal
    const openModal = () => {
      // Populate modal with gift data
      populateModalWithGiftData();

      modalElement.classList.add('active');
      document.body.style.overflow = 'hidden';
    };

    // Close modal
    const closeModal = () => {
      modalElement.classList.remove('active');
      document.body.style.overflow = '';
      // Reset selection
      giftSizeItems.forEach(i => i.classList.remove('product-size--active'));
      selectedSize = null;
    };

    // Event listeners for open/close
    if (openBtn) {
      openBtn.addEventListener('click', openModal);
    }

    if (closeBtn) {
      closeBtn.addEventListener('click', closeModal);
    }

    if (cancelBtn) {
      cancelBtn.addEventListener('click', closeModal);
    }

    // Close modal when clicking overlay
    modalElement.addEventListener('click', (e) => {
      if (e.target === modalElement) {
        closeModal();
      }
    });

    // Handle size selection
    giftSizeItems.forEach(item => {
      item.addEventListener('click', () => {
        // Remove active class from all items
        giftSizeItems.forEach(i => i.classList.remove('product-size--active'));

        // Add active class to clicked item
        item.classList.add('product-size--active');

        // Store selected size
        selectedSize = item.getAttribute('data-size');
      });
    });

    // Handle confirm button
    if (confirmBtn) {
      confirmBtn.addEventListener('click', () => {
        if (selectedSize && sizeBtn) {
          sizeBtn.textContent = `Lấy ngay`;

          // Add gift product to cart with small delay to ensure all scripts are loaded
          setTimeout(() => {
            addGiftProductToCart(selectedSize);
          }, 100);

          closeModal();
        }
      });
    }

    // Close modal with ESC key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && modalElement.classList.contains('active')) {
        closeModal();
      }
    });
  };

  return {
    init: function () {
      initOffcanvasSupport();
      initTabSwitcher();
      changePruductColor();
      initCommentFileUploadPreview();
      initStickyHeightSync();
      initRatingClick();
      initGiftSizeModal();
    },
  };
})();

document.addEventListener("DOMContentLoaded", function () {
  product_detail.init();
});