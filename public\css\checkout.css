.checkout__wrapper{
    font-size: var(--10px);
    position: relative;
}
.checkout__box{
    display: flex;
    align-items: flex-start;
    justify-content: center;
}
.checkout__left{
    width: 65%;
    padding-right: 2em;
    padding-top: 3em;
}
.checkout__right{
    display: block;
    width: 35%;
    padding: 3em 2em;
}

/* ------- Breadcrumb ------- */
.checkout__breadcrumb{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: .5em;
    margin-top: 1em;
}
.checkout__breadcrumb-item:not(:first-child)::before {
    content: ">";
    margin: 0 .5em;
    color: var(--black);
    font-weight: 600;
    font-size: 1.2em;
}
.checkout__breadcrumb-item >a,
.checkout__breadcrumb-item >span{
    font-size: 1.2em;
    font-weight: 500;
}

/* ------------- Content Form ------------- */
.checkout__main-header{
    padding-bottom: 1em;
}
.form__header{
    margin-bottom: 1.5em;
}
.form__header >h2,
.order__shipping-method >h2,
.checkout__payment-method__box >h2{
    font-weight: 600;
    font-size: 1.55em;
}

.form__auth-redirect{
    font-size: var(--10px);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: .5em;
    margin-bottom: .75em;
}
.form__auth-redirect >p{
    margin-bottom: 0;
    font-size: 1.2em;
    color: rgba(var(--rgb-black), .5);
}
.form__auth-redirect >a{
    font-size: 1.2em;
    font-weight: 500;
    color: rgb(var(--rgb-blue));
}

/* --- Customer input field --- */
.customer__inuput-field, .form__note, .summary-discount__content{
    position: relative;
    width: 100%;
    padding: 0.45em 0;
}
.customer__inuput-field >input{
    font-size: var(--10px);
    width: 100%;
    height: 3.5em;
    background-color: transparent;
    padding: 1.2em 1em 0.4em;
    box-sizing: border-box;
    border: .08em solid rgba(var(--rgb-black), .3);
    border-radius: .5em;
    transition: border-color 0.3s ease
}
.customer__inuput-field > input:focus{
    border-color: var(--primary);
    outline: none;
}
.customer__inuput-field >label.shipping__label{
    position: absolute;
    left: .5em;
    top: .5em;
    color: rgba(var(--rgb-black), .3);
    font-size: 1.2em;
    pointer-events: none;
    transition: 0.2s ease all;
}
.customer__inuput-field >input:focus + label,
.customer__inuput-field >input:not(:placeholder-shown) + label{
    top: 0;
    left: .2em;
    font-size: 1em;
    color: var(--primary);
}
.customer__email.customer__inuput-field >input:focus + label,
.customer__email.customer__inuput-field >input:not(:placeholder-shown) + label{
    left: .7em;
}
/* ----- end input field ----- */

.customer__email-phone__box,
.customer__phone-address__box{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.45em 0;
}
.customer__email,
.recipient_street{
    width: 65%;
    padding-left: .5em;
}
.customer__phone,
.recipient__phone{
    width: 35%;
    padding-right: .5em;
}
.customer__street{
    padding: .45em 0;
}

.form__order-toggle{
    display: flex;
    align-items: center;
    gap: 1em;
    margin: 1em 0;
}
.form__order-toggle >input{
    font-size: var(--10px);
    width: 1.3em;
    height: 1.3em;
    cursor: pointer;
}
.form__order-toggle >label{
    font-size: 1.3em;
    font-weight: 500;
    color: rgb(var(--rgb-blue));
    cursor: pointer;
}
/* -------- Address -------- */
.customer__shipping__group{
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1em;
    margin-top: .45em;
}
.shipping__province,
.shipping__district,
.shipping__ward{
    width: 33%;
    position: relative;
    padding: 0.45em 0;
}
.shipping__label{
    font-size: 1em;
    font-weight: normal;
    position: absolute;
    top: 0;
    width: 100%;
    padding: 0 0.93333em;
    z-index: 1;
    user-select: none;
    pointer-events: none;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    box-sizing: border-box;
    color: var(--light-grey);
    transition: all 0.2s ease-out;
    margin-top: 0.8em;
    display: block;
}
.shipping__province >select,
.shipping__district >select,
.shipping__ward >select{
    font-size: var(--10px);
    font-weight: 500;
    border: .08em solid rgba(var(--rgb-black), .3);
    transition: all 0.2s ease-out;
    background-color: transparent;
    color: var(--dark-grey);
    border-radius: .3em;
    display: block;
    box-sizing: border-box;
    width: 100%;
    padding: 1.8em 2.8em 0.5em 1em;
    word-break: normal;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.shipping__province::before,
.shipping__district::before,
.shipping__ward::before{
    content:"";
    position: absolute;
    width: .1em;
    height: 50%;
    top: 1.3em;
    bottom: 0;
    right: 3em;
    background-color: rgba(var(--rgb-black), .2);
}

.shipping__province::after,
.shipping__district::after,
.shipping__ward::after{
  content: "⌄";
  position: absolute;
  top: 45%;
  right: .7em;
  font-size: 1.5em;
  transform: translateY(-50%);
  pointer-events: none;
  color: rgba(var(--rgb-black), 0.5);
}

/* ------ Note in column left ------ */

.note_label{
    font-size: 1em;
    font-weight: normal;
    position: absolute;
    top: 0;
    width: 100%;
    padding: 0 0.93333em;
    user-select: none;
    pointer-events: none;
    overflow: hidden;
    text-overflow: ellipsis;
    box-sizing: border-box;
    color: var(--light-grey);
    transition: all 0.2s ease-out;
    margin-top: 0.8em;
    display: block;
}
.form__note{
    position: relative;
    width: 100%;
    padding: 0.45em 0;
}
.form__note >textarea{
    font-size: var(--10px);
    width: 100%;
    background-color: transparent;
    padding: 1.5em 1em 0.4em;
    box-sizing: border-box;
    border: .08em solid rgba(var(--rgb-black), .3);
    border-radius: .5em;
    transition: border-color 0.3s ease;
    max-height: 10em;
    min-height: 3.5em;
    resize: vertical;
}
.form__note >textarea:focus{
    border-color: var(--primary);
    outline: none;
}
.form__note >label.note_label{
    position: absolute;
    left: .5em;
    top: .5em;
    color: rgba(var(--rgb-black), .3);
    font-size: 1.2em;
    pointer-events: none;
    transition: 0.2s ease all;
}
.form__note >textarea:focus + label,
.form__note >textarea:not(:placeholder-shown) + label{
    top: 0;
    left: .2em;
    font-size: 1em;
    color: var(--primary);
}

/* -------- Order right -------- */

.order-detail__item{
    width: 100%;
    font-size: var(--10px);
    display: flex;
    align-items: center;
    border-bottom: .1em solid rgba(var(--rgb-black), 0.2);
    padding-bottom: 1.3em;
    margin-bottom: 1.3em;
    gap: 2em;
}
.order-detail__item-thumb{
    position: relative;
    width: 5.5em;
    height: 5.5em;
}
.order-detail__item-thumb >img{
    width: 100%;
    height: 100%;
    border-radius: 1em;
    object-fit: cover;
    object-position: center;
}
.order-detail__item-quantity{
    position: absolute;
    top: -.7em;
    right: -.5em;
    z-index: 2;
    color: var(--white);
    background-color: var(--light-grey);
    border-radius: 2em;
    width: 1.5em;
    height: 1.5em;
    display: flex;
    align-items: center;
    justify-content: center;
}
.order-detail__item-info{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1.5em;
}
.order-detail__item-info >span{
    font-size: 1.38em;
    font-weight: 500;
}
.order__item-info__box >h6{
    font-size: 1.38em;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: justify;
}
.order__item-info__box >span{
    font-size: 1.2em;
    font-weight: 400;
    color: rgba(var(--rgb-black), 0.5);
}
.order__item-info__box > span:not(:first-of-type)::before {
  content: "/";
  margin: 0 .3em;
  color: rgba(var(--rgb-black), 0.3);
  font-weight: 600;
  font-size: 1.2em;
}
.order__item-price {
    font-size: var(--10px);
    line-height: 1;
}
.order__item-price>span{
    font-size: 1.38em;
    font-weight: 500;
}
.order__item-price>span + span{
    font-size: 1.2em;
    color: rgba(var(--rgb-black), 0.7);
    font-weight: 500;
    margin-left: 0.5em;
    text-decoration: line-through;
}

/* -------- Order summary -------- */
.order__summary-subtotal{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.3em;
}
.order__summary-subtotal >span,
.order__summary-shipping >span,
.order__summary-discount-price >span{
    font-size: 1.38em;
    font-weight: 500;
}
.order__summary-shipping{
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding-top: 1.3em;
}

.order__summary-total{
    padding: 2em 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.order__summary-total >span{
    font-size: 1.38em;
    font-weight: 500;
}
.order__summary-total >span + span{
    font-size: 2.05em;
    font-weight: 600;
    color: var(--primary);
}
.order__summary-discount-price{
    display: flex;
    justify-content: space-between;
    padding-top: 1.3em;
    border-bottom: .1em solid rgba(var(--rgb-black), 0.2);
    padding: 1.3em 0;
}

.payment-method__checkbox,
.shipping-method__wrapper,
.shipping-method__checkbox{
    display: flex;
    align-items: center;
    gap: 1.5em;
}
.shipping-method__wrapper{
    justify-content: space-between;
    border: .1em solid rgba(var(--rgb-black), 0.2);
    border-radius: 1em;
    padding: 1.5em;
}
.shipping-method__wrapper:last-child{
    margin-bottom: 1.3em;
}
.payment-method__checkbox >input,
.shipping-method__checkbox >input{
    font-size: var(--10px);
    width: 1.4em;
    height: 1.4em;
    font-weight: 500;
    cursor: pointer;
}
.payment-method__checkbox >label,
.shipping-method__checkbox >label,
.shipping-method__wrapper >span{
    font-size: 1.38em;
    font-weight: 500;
    cursor: pointer;
}
.payment-method__checkbox >svg{
    width: 3em;
    height: 3em;
}

.checkout__payment-method__box{
    margin-top: 1.5em;
}
.checkout__payment-methods{
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    border: .1em solid rgba(var(--rgb-black), 0.2);
    border-radius: 1em;
    margin: 1.5em 0;
}
.checkout__payment-methods >.payment-method__item{
    padding: 1.3em;
}
.checkout__payment-methods >.payment-method__item:not(:last-child){
    border-bottom: .1em solid rgba(var(--rgb-black), 0.2);
}
.payment-method__body >p{
    font-size: 1.2em;
    font-weight: 400;
    margin-top: 1em;
    color: rgba(var(--rgb-black), 0.6);
}
.payment-method__what-paypal{
    color: rgba(var(--rgb-black), 0.5);
}
.checkout__privacy-note >p{
    font-weight: 400;
    font-size: 1.2em;
    margin: 1.38em 0;
    color: rgba(var(--rgb-black), 0.5);
}
.checkout__privacy-note >p>a{
    color: var(--primary);
    text-decoration: underline;
}
.checkout__form-group{
    margin-bottom: 1.4em;
}
.form__checkbox-wrapper{
    display: flex;
    align-items: center;
    gap: 1.5em;
}
.form__checkbox-wrapper >.form__checkbox-label{
    font-size: 1.2em;
    font-weight: 400;
    color: rgba(var(--rgb-black), 0.5);
    cursor: pointer;
}
.form__checkbox-wrapper >input{
    font-size: var(--10px);
    width: 1.3em;
    height: 1.3em;
}

.order__shipping-method{
    border-bottom: .1em solid rgba(var(--rgb-black), 0.2);
    padding: 1.3em 0;
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.summary-discount__content >input{
    font-size: var(--10px);
    width: 100%;
    height: 3.5em;
    background-color: transparent;
    padding: 1.2em 1em 0.4em;
    box-sizing: border-box;
    border: .08em solid rgba(var(--rgb-black), .3);
    border-radius: .5em;
    transition: border-color 0.3s ease
}
.summary-discount__content > input:focus{
    border-color: var(--primary);
    outline: none;
}
.summary-discount__content >label.discount-label{
    position: absolute;
    left: 1em;
    top: 1.4em;
    color: rgba(var(--rgb-black), .3);
    font-size: 1.2em;
    pointer-events: none;
    transition: 0.2s ease all;
}
.summary-discount__content >input:focus + label,
.summary-discount__content >input:not(:placeholder-shown) + label{
    top: .7em;
    left: 1em;
    font-size: 1em;
    color: var(--primary);
}

.summary-discount__box-content{
    display: flex;
    align-items: center;
    margin: 1.3em 0;
    gap: 1.5em;
}
.summary-discount__box-content > button {
  white-space: nowrap;
  height: 100%;
  padding: 1em 1.8em;
  border: none;
  border-radius: 0.3em;
  font-size: 1em;
  font-weight: 500;
  transition: background-color 0.3s ease, color 0.3s ease;
}
.summary-discount__box-content >button>span{
    font-size: 1.5em;
    font-weight: 500;
}

.summary-discount__box-content > button:disabled {
height: 100%;
  background-color: var(--light-grey);
  color: var(--white);
  cursor: not-allowed;
}

.summary-discount__box-content > button:not(:disabled) {
  background-color: var(--black);
  color: var(--white);
  cursor: pointer;
}
.discount__choose-coupons{
    display: flex;
    flex-direction: column;
    gap: 1.5em;
}
.discount__choose-coupons__content{
    display: flex;
    align-items: center;
    gap: 1em;
    cursor: pointer;
}
.discount__choose-coupons__content >svg{
    width: 2.5em;
    height: 2.5em;
    fill: rgb(var(--rgb-blue));

}
.discount__choose-coupons__content >span{
    font-size: 1.2em;
    font-weight: 500;
    color: rgb(var(--rgb-blue));
}
.cart-vouchers__chip > span{
    font-size: 1.2em;
}

/* -------- Btn submit -------- */

.btn{
  padding: 1.1em 1.75em;
  cursor: pointer;
  border-radius: 1em;
  border: .1em solid var(--black);
}
.btn--primary > span{
    font-size: 1.4em;
    font-weight: 600;
    text-transform: uppercase;
}

.btn--primary{
  position: relative;
  overflow: hidden;
  background: var(--black);
  display: flex;
  color: var(--white);
  margin-right: 1em;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn--primary:hover{
  color: var(--white);
}
.btn--primary::after{
  content: "";
  position: absolute;
  top:0;
  left: 0;
  opacity: 0.8;
  visibility: hidden;
  height: 100%;
  width: 100%;
  transform: translateX(-105%);
  border-right: 0.2em solid var(--white);
  background-color: rgba(255, 255, 255, .5);
  transition: all 0.5s ease;
  pointer-events: none;
}
.btn--primary:hover::after{
  transform: translate(0);
  opacity: 0;
  visibility: visible;
}

/* ------ Offcanvas vouchers ------ */
.voucher__list {
    display: flex;
    flex-direction: column;
    gap: 2em;
}

.voucher-item-wrapper{
    display: flex;
    align-items: center;
    width: 100%;
    gap: 1em;
}

.voucher-item-wrapper__radio > input{
    font-size: var(--10px);
    width: 2em;
    height: 2em;
    border: 0.1em solid var(--black);
}
.voucher-item{
    width: 100%;
}
.voucher__list > label:last-child {
    opacity: 0.5;
    pointer-events: none;
    position: relative;
}
.voucher__list > label:last-child::after {
    content: "Voucher không khả dụng";
    position: absolute;
    top: 25%;
    left: 75%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.85);
    color: var(--primary);
    font-weight: bold;
    padding: 0.5em 1em;
    border-radius: 0.3em;
    font-size: 0.9em;
    z-index: 1;
}

/* -------- Content second -------- */
.checkout__summary__mobile, .checkout__btn__mobile{
    display: none;
}

/* -------- Responsive -------- */
@media (max-width: 768px){
    .checkout__box{
        flex-direction: column;
        padding: 0 1.5em;
    }
    .checkout__left{
        width: 100%;
        padding-right: 0;
    }
    .checkout__right{
        width: 100%;
        padding: 2em 0;
        display: none;
    }
    .checkout__summary__mobile, .checkout__btn__mobile{
        display: block;
        margin-bottom: 2em;
    }
    .checkout__summary__mobile{
        margin-top: 2em;
    }
    .checkout__main-content .checkout__payment-method__box{
        display: none;
    }
    .order__item-info__box >h6, .order__item-info__box >span{
        max-width: 14em;
        font-size: 1.4em;
    }
    .customer__email-phone__box, .customer__phone-address__box, .customer__shipping__group{
        flex-direction: column;
    }
    .customer__phone, .recipient__phone, .customer__email, .recipient_street, .shipping__province, .shipping__district, .shipping__ward{
        width: 100%;
    }
    .customer__email, .recipient_street{
        padding-left: 0;
    }
    .customer__email{
        padding-top: .8em;
    }
    .sheet__panel{
        width: 90%;
    }
    .order-detail__item, .order__item-price{
        font-size: 2vw;
    }
    .summary-discount__box-content > button{
        padding: 1.2em 1.8em;
    }
    .customer__inuput-field >label.shipping__label, .form__note >label.note_label{
        left: .3em;
        top: .8em;
    }
    .summary-discount__content >label.discount-label{
        top: 1.55em;
    }
    .form__header >h2,
    .order__shipping-method >h2,
    .checkout__payment-method__box >h2{
        font-size: 2.1em;
    }
    .checkout__breadcrumb-item >a, .checkout__breadcrumb-item >span{
        font-size: 1.4em;
    }
    .payment-method__checkbox >label, .shipping-method__checkbox >label, .shipping-method__wrapper >span{
        font-size: 1.7em;
        font-weight: 400;
    }
    .payment-method__body >p{
        font-size: 1.4em;
    }
    .summary-discount__content >input, .customer__inuput-field >input{
        height: 4.5em;
    }
    .discount__choose-coupons__content >span{
        font-size: 1.4em;
    }
    .shipping__province >select, .shipping__district >select, .shipping__ward >select{
        padding: 2.15em 2.8em 1em .8em;
    }
    .customer__phone, .recipient__phone{
        padding-right: 0;
    }
    .customer__shipping__group{
        margin-top: 0;
        padding: .45em 0;
        gap: .45em;
    }
    .checkout_btn{
        padding: 2.5em 1.5em;
        margin-right: 0;
    }
    .checkout_btn > span {
        font-size: 2.2em;
    }
}