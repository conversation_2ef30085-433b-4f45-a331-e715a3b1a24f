package partials

import "github.com/networld-solution/gos/templates"

templ ExtraNavMobile() {
    <div class="m_extra_nav">
        <div class="m_extra_nav__item">
            <a href={templates.SafeURL("/uu-dai")} title="Ưu Đãi" class="m_extra_nav__link">
                <svg class="m_extra_nav__icon" width="40" height="40">
                    <use xlink:href="#icon-discount"></use>
                </svg>
                <span class="m_extra_nav__text">Ưu Đãi</span>
            </a>
        </div>
        <div class="m_extra_nav__item">
            <a href={templates.SafeURL("/ban-chay")} title="Bán chạy" class="m_extra_nav__link">
                <svg class="m_extra_nav__icon" width="40" height="40">
                    <use xlink:href="#icon-shirt-air"></use>
                </svg>
                <span class="m_extra_nav__text"><PERSON><PERSON> chạ<PERSON></span>
            </a>
        </div>
        <div class="m_extra_nav__item">
            <a href={templates.SafeURL("/tim-kiem")} title="Tìm kiếm" class="m_extra_nav__link">
                <svg class="m_extra_nav__icon" width="40" height="40">
                    <use xlink:href="#icon-search-line"></use>
                </svg>
                <span class="m_extra_nav__text">Tìm Kiếm</span>
            </a>
        </div>
        <div class="m_extra_nav__item">
            <a href={templates.SafeURL("/yeu-thich")} title="Yêu thích" class="m_extra_nav__link">
                <svg class="m_extra_nav__icon" width="40" height="40">
                    <use xlink:href="#icon-heart"></use>
                </svg>
                <span class="m_extra_nav__text">Yêu Thích</span>
            </a>
        </div>
        <div class="m_extra_nav__item">
            <a href={templates.SafeURL("/gio-hang")} title="Giỏ hàng" class="m_extra_nav__link">
                <svg class="m_extra_nav__icon" width="40" height="40">
                    <use xlink:href="#icon-shopping-cart"></use>
                </svg>
                <span class="m_extra_nav__count">4</span>
                <span class="m_extra_nav__text">Giỏ Hàng</span>
            </a>
        </div>       
    </div>
}