package product

import (
	"github.com/networld-solution/gos/templates"
	"goweb/views/layouts"
	"goweb/views/product/components"
    "goweb/views/partials"
)

templ Products() {
	@layouts.Master(nil, &[]templ.Component{headProduct()}, scriptProduct()) {
		@components.ProductBackground()
		<section class="list-product">
			<div class="container90">
				<div class="list-product__layout">
					<!-- Sidebar Backdrop -->
					<div class="list-product__sidebar-backdrop"></div>

					<!-- Sidebar Filter -->
					@components.SidebarFilter()

					<!-- Main Content Area : List Product -->
					@components.ListProduct()
				</div>
			</div>
		</section>
        @partials.OffcanvasCart()
	}
}

templ headProduct() {
    <link rel="stylesheet" href={ templates.AssetURL("/static/css/list-product.css") }/>
}

templ scriptProduct() {
	<script src={ templates.AssetURL("/static/js/list-product.js") }></script>
}
