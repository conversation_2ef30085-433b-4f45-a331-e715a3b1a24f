package components

import "goweb/views/partials"

templ ListProductWithCategories() {
<div class="list-product__main">
	<div class="list-product__filter-wrapper">
		<div class="list-product__filter-right">
			<div class="list-product__panel-btn">
				<svg class="list-product__panel-icon">
					<use xlink:href="#icon-filter-sliders"></use>
				</svg>
				<span>Bộ lọc</span>
			</div>
			<div class="list-product__dropdown">
				<button type="button" class="list-product__dropdown-btn">
					<span class="list-product__dropdown-text">M<PERSON><PERSON> nhất</span>
					<div class="list-product__dropdown-icon">
						<svg class="dropdown-arrow">
							<use xlink:href="#icon-dropdown-arrow"></use>
						</svg>
					</div>
				</button>
				<div class="list-product__dropdown-menu">
					<a href="#" class="list-product__dropdown-item list-product__dropdown-item--active"><PERSON><PERSON><PERSON> nhất</a>
					<a href="#" class="list-product__dropdown-item"><PERSON><PERSON> biến</a>
					<a href="#" class="list-product__dropdown-item">Đánh giá cao</a>
					<a href="#" class="list-product__dropdown-item">Giá thấp đến cao</a>
					<a href="#" class="list-product__dropdown-item">Giá cao đến thấp</a>
				</div>
			</div>
		</div>
	</div>
	<!-- Product SlideShow -->
	<div class="for-you-product__slideshows">
		<div class="for-you-product__wrapper">
			<div class="for-you-product__header">
				<div class="sec-head">
					<h2 class="sec-head__title">Đầm</h2>
					<div class="sec-head__btn">
						<a href="#" class="btn-primary">Xem tất cả</a>
					</div>
				</div>
			</div>
			<div class="swiper for-you-product__swiper">
				<div class="swiper-wrapper">
					{{a:=1}}
					for i:=1;i<=2; i++ {
						@partials.ProductItem(" swiper-slide")
						{{a = a+3}}
					}
				</div>

				<div class="swiper-button-next for-you-product__next"></div>
				<div class="swiper-button-prev for-you-product__prev"></div>
			</div>
		</div>
		<div class="for-you-product__wrapper">
			<div class="for-you-product__header">
				<div class="sec-head">
					<h2 class="sec-head__title">Đầm</h2>
					<div class="sec-head__btn">
						<a href="#" class="btn-primary">Xem tất cả</a>
					</div>
				</div>
			</div>
			<div class="swiper for-you-product__swiper">
				<div class="swiper-wrapper">
					{{a=1}}
					for i:=1;i<=2; i++ {
						@partials.ProductItem(" swiper-slide")
						{{a = a+3}}
					}
				</div>

				<div class="swiper-button-next for-you-product__next"></div>
				<div class="swiper-button-prev for-you-product__prev"></div>
			</div>
		</div>
		<div class="for-you-product__wrapper">
			<div class="for-you-product__header">
				<div class="sec-head">
					<h2 class="sec-head__title">Đầm</h2>
					<div class="sec-head__btn">
						<a href="#" class="btn-primary">Xem tất cả</a>
					</div>
				</div>
			</div>
			<div class="swiper for-you-product__swiper">
				<div class="swiper-wrapper">
					{{a=1}}
					for i:=1;i<=2; i++ {
						@partials.ProductItem(" swiper-slide")
						{{a = a+3}}
					}
				</div>

				<div class="swiper-button-next for-you-product__next"></div>
				<div class="swiper-button-prev for-you-product__prev"></div>
			</div>
		</div>
	</div>
</div>
}