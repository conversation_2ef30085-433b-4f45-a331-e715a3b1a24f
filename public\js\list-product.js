/**
 * List Product Page JavaScript
 * Handles price slider, filters, and interactive elements
 */

document.addEventListener('DOMContentLoaded', function () {

    // Initialize Price Range
    initializePriceRange();

    // Initialize Filter Interactions
    initializeFilters();

    // Initialize Dropdown
    initializeDropdown();

    // Initialize Mobile Sidebar
    initializeMobileSidebar();

    // Initialize Material Filter
    initializeMaterialFilter();

    // Initialize Style Filter
    initializeStyleFilter();

    // Initialize Load More
    initializeLoadMore();

    // Initialize Collection Description
    initializeCollectionDescription();
});

/**
 * Initialize Price Range Buttons
 */
function initializePriceRange() {
    const priceMinInput = document.getElementById('price-min');
    const priceMaxInput = document.getElementById('price-max');
    const priceTags = document.querySelectorAll('.price-tag');

    // Handle price input changes
    if (priceMinInput && priceMaxInput) {
        // Setup input handlers
        setupPriceInput(priceMinInput);
        setupPriceInput(priceMaxInput);
    }

    // Handle price tags (0-100k, 100k-200k, etc.)
    priceTags.forEach(tag => {
        tag.addEventListener('click', function () {
            // Remove active from all tags
            priceTags.forEach(t => t.classList.remove('price-tag--active'));

            // Add active to clicked tag
            this.classList.add('price-tag--active');

            // Get min and max values from data attributes
            const minPrice = this.getAttribute('data-min');
            const maxPrice = this.getAttribute('data-max');

            // Set values to inputs with formatting
            if (priceMinInput && priceMaxInput) {
                if (minPrice) {
                    priceMinInput.value = formatPrice(minPrice);
                }
                if (maxPrice) {
                    priceMaxInput.value = formatPrice(maxPrice);
                } else {
                    priceMaxInput.value = '';
                }
            }

            const priceRange = this.textContent.trim();

            // Update results text and filter
            updatePriceFilterResults(priceRange);

            const actualMaxPrice = maxPrice || 999999999;
            filterProductsByPriceRange(minPrice, actualMaxPrice);
        });
    });
}



/**
 * Setup price input with all necessary handlers
 */
function setupPriceInput(input) {
    input.addEventListener('input', function (e) {
        let rawValue = e.target.value;

        // Remove all non-digit characters
        let numbersOnly = rawValue.replace(/[^\d]/g, '');

        // Giới hạn tối đa 11 ký tự
        if (numbersOnly.length > 11) {
            numbersOnly = numbersOnly.substring(0, 11);
        }

        // Format lại value thành dạng có dấu chấm
        const formatted = formatPrice(numbersOnly);
        e.target.value = formatted;
    });

    input.addEventListener('blur', function () {
        triggerPriceFilter();
    });
}

/**
 * Format price real-time with dots
 */
function formatPriceRealtime(value) {
    if (!value) return '';

    // Convert to string and ensure only digits
    let str = value.toString();

    // Add dots every 3 digits from right
    return str.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}
function formatPrice(value) {
    if (!value) return '';
    let str = value.toString().replace(/[^\d]/g, '');
    if (!str) return '';
    return str.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

/**
 * Parse price back to number
 */
function parsePrice(value) {
    if (!value) return 0;
    let str = value.toString().replace(/[^\d]/g, '');
    return parseInt(str) || 0;
}

/**
 * Trigger price filtering
 */
function triggerPriceFilter() {
    const priceMinInput = document.getElementById('price-min');
    const priceMaxInput = document.getElementById('price-max');

    if (!priceMinInput || !priceMaxInput) return;

    const minValue = priceMinInput.value;
    const maxValue = priceMaxInput.value;

    // Only filter if both values exist
    if (minValue && maxValue) {
        const minPrice = parsePrice(minValue);
        const maxPrice = parsePrice(maxValue);

        if (minPrice > 0 && maxPrice > 0) {
            // Clear active tags
            const priceTags = document.querySelectorAll('.price-tag');
            priceTags.forEach(tag => tag.classList.remove('price-tag--active'));

            // Update results and filter
            const filterText = `${minPrice.toLocaleString('vi-VN')} - ${maxPrice.toLocaleString('vi-VN')} VNĐ`;
            updatePriceFilterResults(filterText);
            filterProductsByPriceRange(minPrice, maxPrice);
        }
    }
}

/**
 * Update price filter results text
 */
function updatePriceFilterResults(filterText) {
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = `Lọc theo giá: ${filterText}`;
    }
}

/**
 * Initialize Material Filter
 */
function initializeMaterialFilter() {
    const materialToggle = document.querySelector('.material-toggle');
    const hiddenItems = document.querySelectorAll('.material-item.hidden');

    if (!materialToggle || hiddenItems.length === 0) return;

    let isExpanded = false;

    materialToggle.addEventListener('click', function () {
        isExpanded = !isExpanded;

        hiddenItems.forEach(item => {
            if (isExpanded) {
                item.classList.remove('hidden');
            } else {
                item.classList.add('hidden');
            }
        });

        // Update button text and style
        if (isExpanded) {
            this.textContent = '- Thu gọn';
            this.classList.add('expanded');
        } else {
            this.textContent = '+ Xem thêm';
            this.classList.remove('expanded');
        }
    });

    // Handle material filter selection
    const materialInputs = document.querySelectorAll('.material-input');
    materialInputs.forEach(input => {
        input.addEventListener('change', function () {
            const selectedMaterials = Array.from(materialInputs)
                .filter(inp => inp.checked)
                .map(inp => inp.value);

            // Update results text
            if (selectedMaterials.length > 0) {
                const resultsText = document.querySelector('.list-product__results-text');
                if (resultsText) {
                    resultsText.textContent = `Lọc theo chất liệu: ${selectedMaterials.length} mục`;
                }
            }

        });
    });
}


/**
 * Initialize Style Filter
 */
function initializeStyleFilter() {
    const styleToggle = document.querySelector('.style-toggle');
    const hiddenItems = document.querySelectorAll('.style-item.hidden');

    if (!styleToggle || hiddenItems.length === 0) return;

    let isExpanded = false;

    styleToggle.addEventListener('click', function () {
        isExpanded = !isExpanded;

        hiddenItems.forEach(item => {
            if (isExpanded) {
                item.classList.remove('hidden');
            } else {
                item.classList.add('hidden');
            }
        });

        // Update button text and style
        if (isExpanded) {
            this.textContent = '- Thu gọn';
            this.classList.add('expanded');
        } else {
            this.textContent = '+ Xem thêm';
            this.classList.remove('expanded');
        }
    });

    // Handle style filter selection
    const styleInputs = document.querySelectorAll('.style-input');
    styleInputs.forEach(input => {
        input.addEventListener('change', function () {
            const selectedStyles = Array.from(styleInputs)
                .filter(inp => inp.checked)
                .map(inp => inp.value);

            // Update results text
            if (selectedStyles.length > 0) {
                const resultsText = document.querySelector('.list-product__results-text');
                if (resultsText) {
                    resultsText.textContent = `Lọc theo style: ${selectedStyles.length} mục`;
                }
            }
        });
    });
}

/**
 * Filter products by price range
 */
function filterProductsByPriceRange(minPrice, maxPrice) {
    // Add your filtering logic here
    updatePriceFilterUI(`${minPrice} - ${maxPrice}`);
}

/**
 * Trigger price filter with debouncing
 */
let priceFilterTimeout;
function triggerPriceFilter(minPrice, maxPrice) {
    clearTimeout(priceFilterTimeout);
    priceFilterTimeout = setTimeout(() => {
        // Add visual feedback
        updatePriceFilterUI(minPrice, maxPrice);
    }, 300);
}

/**
 * Update price filter UI
 */
function updatePriceFilterUI(filterText) {
    // Add active class to price widget
    const priceWidget = document.querySelector('.list-product__widget');
    if (priceWidget) {
        priceWidget.classList.add('list-product__widget--active');
    }

    // Update results text is handled by updatePriceFilterResults function
}

/**
 * Filter products by price - UI only
 */
function filterProductsByPrice(minPrice, maxPrice) {
    // Update UI to show filter is active
    updatePriceFilterUI(minPrice, maxPrice);
}

/**
 * Update color filter UI
 */
function updateColorFilterUI(selectedInput) {
    try {
        // Remove active class from all color options
        const allColorOptions = document.querySelectorAll('.list-product__color-option');
        allColorOptions.forEach(option => {
            option.classList.remove('list-product__color-option--active');
        });

        // Add active class to selected option
        const selectedOption = selectedInput.closest('.list-product__color-option');
        if (selectedOption) {
            selectedOption.classList.add('list-product__color-option--active');
        }

    } catch (error) {
        console.error('Error updating color filter UI:', error);
    }
}

/**
 * Filter products by color - UI only
 */
function filterProductsByColor(color) {
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = `Hiển thị sản phẩm có màu: ${color}`;
    }
}

/**
 * Update size filter UI
 */
function updateSizeFilterUI(selectedItem) {
    const size = selectedItem.textContent.trim();

    // Remove active class from all size items
    const allSizeItems = document.querySelectorAll('.list-product__size-item');
    allSizeItems.forEach(item => {
        item.classList.remove('list-product__size-item--active');
    });

    // Add active class to selected item
    selectedItem.classList.add('list-product__size-item--active');

    // Update results text
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = `Hiển thị sản phẩm size: ${size}`;
    }
}

/**
 * Update category filter UI
 */
function updateCategoryFilterUI(selectedLink) {
    const category = selectedLink.textContent.trim();

    // Remove active class from all category links
    const allCategoryLinks = document.querySelectorAll('.list-product__category-link');
    allCategoryLinks.forEach(link => {
        link.classList.remove('list-product__category-link--active');
    });

    // Add active class to selected link
    selectedLink.classList.add('list-product__category-link--active');

    // Update results text
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = `Hiển thị sản phẩm danh mục: ${category}`;
    }
}

/**
 * Initialize filter interactions
 */
function initializeFilters() {
    try {
        // Color filter
        const colorInputs = document.querySelectorAll('.list-product__color-input');

        colorInputs.forEach((input, index) => {

            input.addEventListener('change', function () {
                try {
                    const color = this.value;
                    const colorId = this.id;

                    // Add visual feedback
                    updateColorFilterUI(this);

                    // Add your color filtering logic here
                    filterProductsByColor(color);
                } catch (error) {
                    console.error('Error in color filter change:', error);
                }
            });

        });
    } catch (error) {
        console.error('Error initializing color filters:', error);
    }

    // Size filter
    const sizeItems = document.querySelectorAll('.list-product__size-item');
    sizeItems.forEach(item => {
        item.addEventListener('click', function () {
            const size = this.textContent.trim();
            updateSizeFilterUI(this);
        });
    });

    // Category filter
    const categoryLinks = document.querySelectorAll('.list-product__category-link');
    categoryLinks.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const category = this.textContent.trim();
            updateCategoryFilterUI(this);
        });
    });

    // Reset button
    const resetBtn = document.querySelector('.list-product__reset-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', function (e) {
            e.preventDefault();
            resetAllFilters();
        });
    }
}

/**
 * Reset all filters - UI only
 */
function resetAllFilters() {

    // Reset price range
    const priceMinInput = document.getElementById('price-min');
    const priceMaxInput = document.getElementById('price-max');
    const priceTags = document.querySelectorAll('.price-tag');

    if (priceMinInput) {
        priceMinInput.value = '';
        priceMinInput.placeholder = 'Tối thiểu';
    }
    if (priceMaxInput) {
        priceMaxInput.value = '';
        priceMaxInput.placeholder = 'Tối đa';
    }
    priceTags.forEach(tag => tag.classList.remove('price-tag--active'));

    // Reset color filters
    const colorInputs = document.querySelectorAll('.list-product__color-input');
    colorInputs.forEach(input => input.checked = false);

    // Reset material filters
    const materialInputs = document.querySelectorAll('.material-input');
    materialInputs.forEach(input => input.checked = false);

    // Reset material toggle
    const materialToggle = document.querySelector('.material-toggle');
    const materialHiddenItems = document.querySelectorAll('.material-item.hidden');

    if (materialToggle) {
        materialToggle.textContent = '+ Xem thêm';
        materialToggle.classList.remove('expanded');
    }

    materialHiddenItems.forEach(item => {
        item.classList.add('hidden');
    });

    // Reset style filters
    const styleInputs = document.querySelectorAll('.style-input');
    styleInputs.forEach(input => input.checked = false);

    // Reset style toggle
    const styleToggle = document.querySelector('.style-toggle');
    const styleHiddenItems = document.querySelectorAll('.style-item.hidden');

    if (styleToggle) {
        styleToggle.textContent = '+ Xem thêm';
        styleToggle.classList.remove('expanded');
    }

    styleHiddenItems.forEach(item => {
        item.classList.add('hidden');
    });

    // Remove active color options
    const activeColorOptions = document.querySelectorAll('.list-product__color-option--active');
    activeColorOptions.forEach(option => option.classList.remove('list-product__color-option--active'));

    // Reset size filters
    const sizeItems = document.querySelectorAll('.list-product__size-item');
    sizeItems.forEach(item => item.classList.remove('list-product__size-item--active'));

    // Set default active size (S)
    const defaultSizeItem = document.querySelector('.list-product__size-item');
    if (defaultSizeItem) {
        defaultSizeItem.classList.add('list-product__size-item--active');
    }

    // Reset category filters
    const activeCategoryLinks = document.querySelectorAll('.list-product__category-link--active');
    activeCategoryLinks.forEach(link => link.classList.remove('list-product__category-link--active'));

    // Remove active widget classes
    const activeWidgets = document.querySelectorAll('.list-product__widget--active');
    activeWidgets.forEach(widget => widget.classList.remove('list-product__widget--active'));

    // Reset results text
    const resultsText = document.querySelector('.list-product__results-text');
    if (resultsText) {
        resultsText.textContent = 'Hiển thị 1–5 trong 50 kết quả';
    }
}

/**
 * Initialize dropdown functionality
 */
function initializeDropdown() {
    const dropdown = document.querySelector('.list-product__dropdown');
    const dropdownBtn = document.querySelector('.list-product__dropdown-btn');
    const dropdownMenu = document.querySelector('.list-product__dropdown-menu');
    const dropdownItems = document.querySelectorAll('.list-product__dropdown-item');
    const dropdownText = document.querySelector('.list-product__dropdown-text');

    if (!dropdown || !dropdownBtn || !dropdownMenu) {
        console.log('Dropdown elements not found');
        return;
    }

    // Toggle dropdown
    dropdownBtn.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        dropdown.classList.toggle('list-product__dropdown--active');
    });

    // Handle dropdown item selection
    dropdownItems.forEach(item => {
        item.addEventListener('click', function (e) {
            e.preventDefault();

            // Remove active class from all items
            dropdownItems.forEach(i => i.classList.remove('list-product__dropdown-item--active'));

            // Add active class to clicked item
            this.classList.add('list-product__dropdown-item--active');

            // Update dropdown text
            dropdownText.textContent = this.textContent;

            // Close dropdown
            dropdown.classList.remove('list-product__dropdown--active');
        });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function (e) {
        if (!dropdown.contains(e.target)) {
            dropdown.classList.remove('list-product__dropdown--active');
        }
    });
}

/**
 * Initialize mobile sidebar functionality
 */
function initializeMobileSidebar() {
    const panelBtn = document.querySelector('.list-product__panel-btn');
    const sidebar = document.querySelector('.list-product__sidebar');
    const closeBtn = document.querySelector('.list-product__close-btn');
    const backdrop = document.querySelector('.list-product__sidebar-backdrop');

    if (panelBtn && sidebar) {
        panelBtn.addEventListener('click', function (e) {
            e.preventDefault();
            sidebar.classList.add('list-product__sidebar--open');
            if (backdrop) {
                backdrop.classList.add('list-product__sidebar-backdrop--active');
            }
            document.body.classList.add('sidebar-open');
        });
    }

    if (closeBtn && sidebar) {
        closeBtn.addEventListener('click', function (e) {
            e.preventDefault();
            closeSidebar();
        });
    }

    // Close sidebar when clicking backdrop
    if (backdrop) {
        backdrop.addEventListener('click', function (e) {
            e.preventDefault();
            closeSidebar();
        });
    }

    // Close sidebar function
    function closeSidebar() {
        sidebar.classList.remove('list-product__sidebar--open');
        if (backdrop) {
            backdrop.classList.remove('list-product__sidebar-backdrop--active');
        }
        document.body.classList.remove('sidebar-open');
    }

    // Close sidebar when clicking outside (fallback)
    document.addEventListener('click', function (e) {
        if (sidebar && sidebar.classList.contains('list-product__sidebar--open')) {
            const sidebarContent = sidebar.querySelector('.list-product__sidebar-sticky');
            if (!sidebarContent.contains(e.target) && !panelBtn.contains(e.target)) {
                closeSidebar();
            }
        }
    });
}

/**
 * Initialize Load More functionality
 */
function initializeLoadMore() {
    const loadMoreBtn = document.querySelector('.load-more__btn');
    const loadMoreSpinner = document.querySelector('.load-more__spinner');

    if (!loadMoreBtn || !loadMoreSpinner) return;

    loadMoreBtn.addEventListener('click', function () {
        showLoadingSpinner();

        loadMoreBtn.classList.add('hidden');

        // API call
        setTimeout(() => {
            hideLoadingSpinner();
            loadMoreBtn.classList.remove('hidden');

            // Add new products
            loadMoreProducts();
        }, 2000);
    });
}

/**
 * Show loading spinner
 */
function showLoadingSpinner() {
    const spinner = document.querySelector('.load-more__spinner');
    if (spinner) {
        spinner.classList.remove('hidden');
    }
}

/**
 * Hide loading spinner
 */
function hideLoadingSpinner() {
    const spinner = document.querySelector('.load-more__spinner');
    if (spinner) {
        spinner.classList.add('hidden');
    }
}

/**
 * Load more products
 */
function loadMoreProducts() {
    const productGrid = document.querySelector('.list-product__grid');
    if (!productGrid) return;

    // Create new product items
    const newProducts = createProductItems(4); // Load 4 products

    newProducts.forEach(product => {
        productGrid.appendChild(product);
    });
}

/**
 * Create product items
 */
function createProductItems(count) {
    const products = [];

    for (let i = 0; i < count; i++) {
        // Create a div
        const productDiv = document.createElement('div');
        productDiv.className = 'product-item';
        productDiv.innerHTML = `
            <div class="product-item__content">
                <div class="product-item__image">
                    <img src="/static/images/damxoe2.webp" alt="Product ${i + 1}" />
                </div>
                <div class="product-item__info">
                    <h3>Sản phẩm mới ${i + 1}</h3>
                    <p class="product-item__price">299.000 VNĐ</p>
                </div>
            </div>
        `;
        products.push(productDiv);
    }

    return products;
}

/**
 * Initialize Pagination functionality - Removed
 * See pagination_backup.templ for the saved code
 */
function initializePagination() {
    // Function removed - see pagination_backup.templ
    console.log('Pagination functionality has been removed');

}

// Pagination functions removed - see pagination_backup.templ

/**
 * Initialize Collection Description functionality
 */
function initializeCollectionDescription() {
    const collectionBtn = document.querySelector('.collection__btn');
    const collectionContent = document.querySelector('.collection__content');
    const collectionDescription = document.querySelector('.collection__description');

    if (!collectionBtn || !collectionContent || !collectionDescription) return;

    const allSubtitles = collectionContent.querySelectorAll('.collection__subtitle');
    const allTexts = collectionContent.querySelectorAll('.collection__text');
    const initialShowCount = 1;
    let isExpanded = false;

    // Hide items beyond initial count
    hideExtraContent();

    collectionBtn.addEventListener('click', function () {
        if (isExpanded) {
            hideExtraContent();
            updateButtonText('Xem thêm');
            isExpanded = false;

            // Remove active
            collectionDescription.classList.remove('active');

            if (collectionDescription) {
                collectionDescription.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        } else {
            showAllContent();
            updateButtonText('Thu gọn');
            isExpanded = true;

            // Add active
            collectionDescription.classList.add('active');
        }
    });

    /**
     * Hide extra content beyond initial count
     */
    function hideExtraContent() {
        allSubtitles.forEach((subtitle, index) => {
            if (index >= initialShowCount) {
                subtitle.style.display = 'none';
            } else {
                subtitle.style.display = 'block';
            }
        });

        allTexts.forEach((text, index) => {
            if (index >= initialShowCount) {
                text.style.display = 'none';
            } else {
                text.style.display = 'block';
            }
        });
    }

    /**
     * Show all content
     */
    function showAllContent() {
        allSubtitles.forEach(subtitle => {
            subtitle.style.display = 'block';
        });

        allTexts.forEach(text => {
            text.style.display = 'block';
        });
    }

    /**
     * Update button text
     */
    function updateButtonText(text) {
        const btnText = collectionBtn.querySelector('.collection__btn-text');
        if (btnText) {
            btnText.textContent = text;
        }
    }
}
