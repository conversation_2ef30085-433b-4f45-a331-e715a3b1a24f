package trends

import(
    "goweb/views/layouts"
    "github.com/networld-solution/gos/templates"
    "goweb/views/trends/components"
)

templ Trends(){
    @layouts.Master(nil, &[]templ.Component{head()}, scriptSlot()) {
        @components.ProductBackground()
        <section class="list-product">
			<div class="container90">
				<div class="list-product__layout">
					<!-- Sidebar Backdrop -->
					<div class="list-product__sidebar-backdrop"></div>

					<!-- Sidebar Filter -->
					@components.SidebarFilter()

					<!-- Main Content Area : List Product -->
					@components.ListProductTrends()
				</div>
			</div>
		</section>
    }
}

templ head(){
    <link rel="stylesheet" href={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.css")} />
    <link rel="stylesheet" href={templates.AssetURL("/static/css/product.css")}>
    <link rel="stylesheet" href={ templates.AssetURL("/static/css/list-product.css") }/>
    <link rel="stylesheet" href={ templates.AssetURL("/static/css/trends.css") }/>
}
templ scriptSlot(){
    <script type="text/javascript" src={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.js")}></script>
    <script src={ templates.AssetURL("/static/js/list-product.js") }></script>
     <script type="text/javascript" src={templates.AssetURL("/static/js/trends.js")}></script>
}