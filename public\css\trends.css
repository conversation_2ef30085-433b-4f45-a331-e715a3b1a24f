.trends-product__slideshows{
    display: flex;
    flex-direction: column;
    gap: 3em;
}
.list-product__filter-wrapper{
    justify-content: flex-end;
}
.trends-product__wrapper{
  font-size: 0.652vw;
}
.trends-product__header .sec-head>.sec-head__btn>a{
  font-size: 1.3em;
}
.swiper-button-next.trends-product__next, .swiper-button-prev.trends-product__prev{
  width: 4em;
  height: 4em;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .2);
  font-size: var(--vw-12);
}
.swiper-button-next.trends-product__next:after, .swiper-button-prev.trends-product__prev:after{
  font-size: 2em;
  color: rgb(var(--rgb-white));
}