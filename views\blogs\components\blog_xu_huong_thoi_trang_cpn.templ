package components

import "github.com/networld-solution/gos/templates"

templ BlogsXuHuongThoiTrangCpn() {
<div class="ana-col" id="burden-section">
    <div class="ana-grid-box">
        <span class="ana-grid-box__title">XU HƯỚNG THỜI TRANG</span>
        <a class="txt-hover-red ana-grid-box__seemore" href="#" title="xu hướng thời trang">
            <span>Xem tất cả</span>
            <div class="lib-grid-box__seemore-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z" />
                </svg>
            </div>
        </a>
    </div>
    <div class="ana-articles">
        @blogXuHuongThoiTrangItem("Xu hướng thời trang Xuân Hè 2025: <PERSON><PERSON><PERSON> sắc tươi sáng",
        "/tin-tuc/xu-huong-xuan-he-2025-mau-sac.html", "/static/images/blogs/blog-trend-spring.png", "Khám phá những gam
        màu tươi sáng và họa tiết độc đáo sẽ thống trị thời trang mùa xuân hè năm nay.", "3.200", "1.150", 0)
        @blogXuHuongThoiTrangItem("Phong cách Minimalist: Xu hướng không bao giờ lỗi thời",
        "/tin-tuc/phong-cach-minimalist-2025.html", "/static/images/blogs/blog-minimalist.jpg", "Tìm hiểu cách áp dụng
        phong cách minimalist vào tủ đồ hàng ngày để luôn thanh lịch và tinh tế.", "2.850", "920", 1)
        @blogXuHuongThoiTrangItem("Thời trang công sở hiện đại cho phụ nữ", "/tin-tuc/thoi-trang-cong-so-hien-dai.html",
        "/static/images/blogs/blog-office-style.jpg", "Cập nhật những xu hướng thời trang công sở mới nhất giúp bạn tự
        tin và chuyên nghiệp.", "4.100", "1.380", 2)
        @blogXuHuongThoiTrangItem("Bộ sưu tập Thu Đông: Elegance & Warmth", "/tin-tuc/bo-suu-tap-thu-dong-2025.html",
        "/static/images/blogs/blog-collection-new.jpg", "Khám phá bộ sưu tập thu đông mới với sự kết hợp hoàn hảo giữa
        sự thanh lịch và ấm áp.", "2.650", "780", 3)
        @blogXuHuongThoiTrangItem("Phong cách Hàn Quốc: K-Fashion Trends",
        "/tin-tuc/phong-cach-han-quoc-k-fashion.html", "/static/images/blogs/blog-collection-new-2.jpg", "Tìm hiểu về
        những xu hướng thời trang Hàn Quốc đang được yêu thích trên toàn thế giới.", "3.750", "1.250", 4)
        @blogXuHuongThoiTrangItem("Thời trang bền vững: Tương lai của ngành công nghiệp",
        "/tin-tuc/thoi-trang-ben-vung-tuong-lai.html", "/static/images/blogs/blog-office-main.webp", "Khám phá xu hướng
        thời trang bền vững và cách lựa chọn trang phục thân thiện với môi trường.", "2.950", "890", 5)
    </div>
    <div class="menubrc-wrapper">
        <div class="menu-line"></div>
        <svg class="btn-show-more" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z" />
        </svg>
    </div>
</div>
}

templ blogXuHuongThoiTrangItem(title, pagePath, img, description, views, likes string, i int) {
<article class={ AddClass(i) }>
    <div class="ana-col__thumb">
        <a href={templ.SafeURL(pagePath)} title={title} class="nitem__thumb-link">
            <img class="ana__thumb-img" src={img} alt={title}>
        </a>
    </div>
    <div class="ana-col__content">
        <h3 class="ana-col__title">
            <a class="txt-hover-green" href={templ.SafeURL(pagePath)} title={title}>{title}</a>
        </h3>
        <div class="ana__desc">
            {description}
        </div>
        <div class="ana__bottom">
            <ul class="ana__support">
                <li class="ana__support-item">
                    <div class="nitem__support-icon">
                        <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                            <use xlink:href="#icon-like-blog"></use>
                        </svg>
                    </div>
                    <span>{likes}</span>
                </li>
                <li class="ana__support-item">
                    <div class="nitem__support-icon">
                        <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                            <use xlink:href="#icon-eye-blog"></use>
                        </svg>
                    </div>
                    <span>{views}</span>
                </li>
            </ul>
            <a class="ana-col__support-item txt-hover-red" href="#" title="">
                <span>Xem ngay ></span>
            </a>
        </div>
    </div>
</article>
}

templ BlogRightBannerCpn() {
<div class="call-cta">
    <img src="/static/images/blogs/blog-collection-new.jpg" alt="Fashion Collection" class="call-cta__img">
    <div class="call-cta__wrapper">
        <div class="call-cta-text">
            <p>Bộ sưu tập mới</p>
            <h3>Khám phá ngay!</h3>
        </div>
        <div class="call-cta__btn">
            <a href="/collections" title="Xem bộ sưu tập" class="btn-primary">XEM BỘ SƯU TẬP <br>
                MỚI NHẤT</a>
            <a href="/stores" title="Tìm cửa hàng" class="btn-primary">TÌM CỬA HÀNG <br> GẦN NHẤT</a>
        </div>
    </div>
</div>
}



func AddClass(i int) string {
if i > 3 {
return "nitem hidden"
}

return "nitem"
}