
name: Deploy to local dev 48
on:
  push:
    branches:
      - dev
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
      - name: SSH Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_S48 }}
          USERNAME: ${{ secrets.USERNAME_S48 }}
          PORT: ${{ secrets.PORT_S48 }}
          KEY: ${{ secrets.PRI_KEY_S48_GOWEB }}
          script: |            
            sudo systemctl stop maybiweb
            destination_dir="/home/<USER>/maybi-web"              
            cd $destination_dir
            git pull origin dev
            sudo systemctl restart maybiweb