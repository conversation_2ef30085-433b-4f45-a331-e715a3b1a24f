package product

import (
    "goweb/views/layouts"
    "github.com/networld-solution/gos/templates"
    "goweb/views/product/components"
	"goweb/views/partials"
)

templ DetailProduct() {
    @layouts.Master(nil, &[]templ.Component{head()}, scriptSlot()) { 
        <div class="product-detail__wrapper">
            <div class="container90">
                @components.BreadCrumbProductDetailCpn()
                <div class="product-detail__half-layout product">
                    <div class="product-detail__content-left">
                        @components.ViewerProductDetailCpn()
                    </div>
                    <div class="product-detail__content-right">
                        @components.ContentProductDetailCpn()
                        @components.InfoProductDetailCpn()
                    </div>
                </div>
                @components.RelatedProduct()
            </div>
        </div>
        @partials.OffcanvasProduct()
        @partials.ContentOffcanvasReturn()
        @partials.ContentOffcanvasSize()
    }    
}
templ head(){
	<link rel="stylesheet" href={templates.AssetURL("/static/css/product_detail.css")}>
	<link rel="stylesheet" href={templates.AssetURL("/static/css/product.css")}>
    <link rel="stylesheet" href={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.css")} />
}

templ scriptSlot(){
    <script type="text/javascript" src={templates.AssetURL("/static/libs/swiper/swiper-bundle.min.js")}></script>
    <script type="text/javascript" src={templates.AssetURL("/static/js/product_detail.js")}></script>
}