package server

import (
	"fmt"
	"goweb/cmd/server/internal"

	"github.com/networld-solution/sctx"
	"github.com/networld-solution/sctx/component/discord"
	"github.com/networld-solution/sctx/component/fiberapp"
	"github.com/networld-solution/sctx/component/mongodb"
	"github.com/networld-solution/sctx/configs"
	"github.com/spf13/cobra"
)

var (
	serviceName = "server-service"
	version     = "1.0.0"
)

func newServiceCtx() sctx.ServiceContext {
	return sctx.NewServiceContext(
		sctx.WithName(serviceName),
		sctx.WithComponent(fiberapp.NewFiber(configs.KeyCompFIBER)),
		sctx.WithComponent(mongodb.NewMongoDB(configs.KeyCompMongoDB, "")),
		sctx.WithComponent(sctx.NewAppLoggerDaily(configs.KeyLoggerDaily)),
		sctx.WithComponent(discord.NewDiscordClient(configs.KeyDiscordSMS)),
	)
}

var (
	// Used for flags.
	ServerCmd = &cobra.Command{
		Use:     "web",
		Short:   "Server web run maybi ecommerce",
		Long:    `Server CLI Long`,
		Version: version,
		Run: func(cmd *cobra.Command, args []string) {
			serviceCtx := newServiceCtx()
			loggerSv := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")
			loggerSv.Info("Start Service Server")

			if err := serviceCtx.Load(); err != nil {
				loggerSv.Fatal(err)
			}

			fiberComp := serviceCtx.MustGet(configs.KeyCompFIBER).(fiberapp.FiberComponent)
			appFiber := fiberComp.GetApp()

			internal.RoutesServer(appFiber, serviceCtx)
			if err := appFiber.Listen(fmt.Sprintf(":%d", fiberComp.GetPort())); err != nil {
				loggerSv.Fatal(err)
			}
		},
	}
)
