const DefaultCountdownMinutes = 5;

const cart = (function () {
  const initializeCountdown = () => {
    const el = document.getElementById("cart-timer");
    if (!el) return;

    const minutes = parseInt(el.dataset.minutes || DefaultCountdownMinutes, 10);
    startCartCountdown(minutes * 60, el.id);
  };


  return {
    init: function () {
      initializeCountdown();
    },
  };
})();

document.addEventListener("DOMContentLoaded", function (event) {
  cart.init();
});

function startCartCountdown(durationSeconds, displayElementId) {
  let remaining = durationSeconds;
  const display = document.getElementById(displayElementId);

  const timer = setInterval(() => {
    const minutes = String(Math.floor(remaining / 60)).padStart(2, '0');
    const seconds = String(remaining % 60).padStart(2, '0');
    display.textContent = `${minutes}:${seconds}`;

    if (remaining <= 0) {
      clearInterval(timer);
      display.textContent = "00:00";
    }

    remaining--;
  }, 1000);
}

