package components

import "github.com/networld-solution/gos/templates"
import "strconv"
import "strings"

templ BlogsPhongCachCpn() {
<div class="ana-col" id="burden-section">
    <div class="ana-grid-box">
        <span class="ana-grid-box__title">PHONG CÁCH THỜI TRANG</span>
        <a class="txt-hover-red ana-grid-box__seemore" href="#" title="phong cách thời trang">
            <span>Xem tất cả</span>
            <div class="lib-grid-box__seemore-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z" />
                </svg>
            </div>
        </a>
    </div>
    <div class="ana-articles">
        @blogPhongCachItem("Phong cách Minimalist: <PERSON> hướ<PERSON> không bao giờ lỗi thời",
        "/tin-tuc/phong-cach-minimalist-2025.html", "/static/images/blogs/blog-minimalist.jpg", "Tìm hiểu cách áp dụng
        phong cách minimalist vào tủ đồ hàng ngày để luôn thanh lịch và tinh tế.", "2.850", "920", 0)
        @blogPhongCachItem("Phong cách Vintage: Quay trở lại với vẻ đẹp cổ điển",
        "/tin-tuc/phong-cach-vintage-co-dien.html", "/static/images/blogs/blog-vintage-style.jpg", "Khám phá cách kết
        hợp trang phục vintage để tạo nên phong cách độc đáo và ấn tượng.", "3.150", "1.080", 1)
        @blogPhongCachItem("Phong cách Hàn Quốc: K-Fashion Trends", "/tin-tuc/phong-cach-han-quoc-k-fashion.html",
        "/static/images/blogs/blog-collection-new-2.jpg", "Tìm hiểu về những xu hướng thời trang Hàn Quốc đang được yêu
        thích trên toàn thế giới.", "3.750", "1.250", 2)
        @blogPhongCachItem("Phong cách Street Style: Thời trang đường phố hiện đại",
        "/tin-tuc/phong-cach-street-style-hien-dai.html", "/static/images/blogs/blog-street-style.jpg", "Cập nhật những
        xu hướng street style mới nhất từ các fashionista trên khắp thế giới.", "2.950", "890", 3)
        @blogPhongCachItem("Phong cách Boho Chic: Tự do và phóng khoáng", "/tin-tuc/phong-cach-boho-chic-tu-do.html",
        "/static/images/blogs/blog-boho-style.jpg", "Khám phá phong cách boho chic với những trang phục thoải mái và đầy
        cá tính.", "2.650", "780", 4)
        @blogPhongCachItem("Phong cách Business Casual: Chuyên nghiệp nhưng thoải mái",
        "/tin-tuc/phong-cach-business-casual.html", "/static/images/blogs/blog-business-casual.jpg", "Tìm hiểu cách phối
        đồ business casual phù hợp cho môi trường công sở hiện đại.", "3.200", "1.150", 5)
    </div>
    <div class="menubrc-wrapper">
        <div class="menu-line"></div>
        <svg class="btn-show-more" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z" />
        </svg>
    </div>
</div>
}

templ blogPhongCachItem(title, pagePath, img, description, views, likes string, i int) {
<article class={ AddClass(i) }>
    <div class="ana-col__thumb">
        <a href={templ.SafeURL(pagePath)} title={title} class="nitem__thumb-link">
            <img class="ana__thumb-img" src={img} alt={title}>
        </a>
    </div>
    <div class="ana-col__content">
        <h3 class="ana-col__title">
            <a class="txt-hover-green" href={templ.SafeURL(pagePath)} title={title}>{title}</a>
        </h3>
        <div class="ana__desc">
            {description}
        </div>
        <div class="ana__bottom">
            <ul class="ana__support">
                <li class="ana__support-item">
                    <div class="nitem__support-icon">
                        <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                            <use xlink:href="#icon-like-blog"></use>
                        </svg>
                    </div>
                    <span>{likes}</span>
                </li>
                <li class="ana__support-item">
                    <div class="nitem__support-icon">
                        <svg width="12" height="11" viewBox="0 0 12 11" fill="none">
                            <use xlink:href="#icon-eye-blog"></use>
                        </svg>
                    </div>
                    <span>{views}</span>
                </li>
            </ul>
            <a class="ana-col__support-item txt-hover-red" href="#" title="">
                <span>Xem ngay ></span>
            </a>
        </div>
    </div>
</article>
}

func AddClass(i int) string {
if i > 3 {
return "nitem hidden"
}

return "nitem"
}

func FormatNumberWithDot(num int64) string {
s := strconv.FormatInt(num, 10)
n := len(s)
if n <= 3 { return s } var b strings.Builder pre :=n % 3 if pre> 0 {
    b.WriteString(s[:pre])
    if n > pre {
    b.WriteString(".")
    }
    }

    for i := pre; i < n; i +=3 { b.WriteString(s[i : i+3]) if i+3 < n { b.WriteString(".") } } return b.String() 
}