/* Deals Event Section */
.deals-event {
    padding: 4em 0;
    width: 100%;
    font-size: 0.652vw;
    overflow: hidden;
}

.deals-event__row {
    display: flex;
    gap: 2em;
    align-items: stretch;
}

.deals-event__products {
    min-width: 0;
    position: relative;
    display: flex;
    width: 60%;
    height: 100%;
}

/* Deals Event Swiper */
.deals-event__products .swiper {
    width: 100%;
    height: auto;
    overflow: hidden;
}

.deals-event__products .swiper-wrapper {
    display: flex;
    flex-direction: row;
    height: auto;
}

.deals-event__products .swiper-slide {
    height: auto;
    flex-shrink: 0;
    display: flex;
    align-items: stretch;
    justify-content: center;
    padding: 0.25em;
    box-sizing: border-box;
}

.deals-event__products .swiper-slide .deals-event__item {
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box;
}

.deals-event__products .swiper-slide .event-card {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--white);
    border-radius: 1em;
    box-shadow: 0 0.25em 1em rgba(var(--rgb-black), 0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--dark-light);
    overflow: hidden;
    box-sizing: border-box;
}

.deals-event__products .swiper-slide .event-card__media {
    position: relative;
    overflow: hidden;
    border-radius: 1em;
    flex: 1;
    min-height: 0;
}

.deals-event__products .swiper-slide .event-card__media img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.deals-event__products .swiper-slide .event-card:hover .event-card__media img {
    transform: scale(1.1);
}

.deals-event__products .swiper-slide .event-card__content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(var(--rgb-white), 0.95);
    backdrop-filter: blur(10px);
    padding: 1em;
    border-radius: 0.75em;
    margin: 0.75em;
    border: 1px solid rgba(var(--rgb-white), 0.2);
}

/* Deals Event Pagination */
.deals-event__products .swiper-pagination {
    position: relative;
    margin-top: 1.5em;
    text-align: center;
}

.deals-event__products .swiper-pagination-bullet {
    width: 0.75em;
    height: 0.75em;
    background: var(--light-grey);
    opacity: 1;
    margin: 0 0.25em;
    transition: var(--transition);
}

.deals-event__products .swiper-pagination-bullet-active {
    background: var(--primary);
    transform: scale(1.2);
}

.deals-event__grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1em;
    /* width: 50%; */
    margin: auto;
}

.deals-event__item {
    width: 100%;
}

.deals-event__hero {
    min-width: 0;
    width: 40%;
}

/* Deals Card */
.deals-card {
    position: relative;
    background: #fff;
    border-radius: 0.5em;
    overflow: hidden;
    box-shadow: 0 0.125em 0.25em rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.deals-card:hover {
    transform: translateY(-0.25em);
    box-shadow: 0 0.5em 1em rgba(0, 0, 0, 0.15);
}

.deals-card__media {
    position: relative;
    overflow: hidden;
}

.deals-card__media img {
    width: 100%;
    height: 20em;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.deals-card:hover .deals-card__media img {
    transform: scale(1.05);
}

.deals-card__content {
    padding: 1.25em;
}

.deals-card__sale {
    display: inline-block;
    background: #dc3545;
    color: #fff;
    padding: 0.25em 0.75em;
    border-radius: 1em;
    font-size: 0.75em;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 0.75em;
}

.event-card__sale--success {
    background: var(--primary);
}

.deals-card__title {
    margin: 0.5em 0;
    font-size: 1em;
    font-weight: 600;
}

.deals-card__title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.deals-card__title a:hover {
    color: #007bff;
}

.deals-card__price {
    font-size: 1.125em;
    font-weight: 700;
    color: #dc3545;
    margin: 0;
}

.deals-card__price del {
    color: #6c757d;
    font-weight: 400;
    margin-left: 0.5em;
}

/* Sale Badge */
.deals-card__badge {
    position: absolute;
    top: 1em;
    right: 1em;
    background: #ffc107;
    color: #333;
    padding: 0.75em;
    border-radius: 50%;
    font-size: 0.875em;
    font-weight: 700;
    text-align: center;
    line-height: 1.2;
    min-width: 5em;
    min-height: 5em;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 2;
}

.deals-card__badge img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
    width: 100%;
    height: 100%;
    animation: rotate-360 20s linear infinite;
}

/* Event Card */
.event-card {
    position: relative;
    background: var(--white);
    border-radius: 1em;
    box-shadow: 0 0.25em 1em rgba(var(--rgb-black), 0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--dark-light);
}

.event-card:hover {
    transform: translateY(-0.5em);
    box-shadow: 0 1em 2em rgba(var(--rgb-black), 0.15);
}

.event-card__media {
    position: relative;
    overflow: hidden;
    border-radius: 1em;
}

.event-card__media img {
    width: 100%;
    aspect-ratio: 3 / 4;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.event-card:hover .event-card__media img {
    transform: scale(1.1);
}

.event-card__content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(var(--rgb-white), 0.95);
    backdrop-filter: blur(10px);
    padding: 1em;
    border-radius: 0.75em;
    margin: 0.75em;
    border: 1px solid rgba(var(--rgb-white), 0.2);
}

.event-card__sale {
    display: inline-block;
    background: var(--red);
    color: var(--white);
    padding: 0.25em 0.75em;
    border-radius: 0.5em;
    font-size: 0.75em;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 0.5em;
}

.sale-success {
    background: #28a745;
}

.event-card__title {
    /* margin: 0.5em 0; */
    font-size: 1.4em;
    font-weight: 600;
    line-height: 1.3;
}

.event-card__title a {
    color: var(--title);
    text-decoration: none;
    transition: color 0.3s ease;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
}

.event-card__title a:hover {
    color: var(--primary);
}

.event-card__price {
    display: flex;
    align-items: center;
    gap: 0.5em;
    margin-top: 0.2em;
}

.event-card__current {
    font-size: 1.125em;
    font-weight: 700;
    color: var(--primary);
}

.event-card__original {
    font-size: 0.875em;
    color: var(--light-grey);
    text-decoration: line-through;
}

/* Event Card Rating */
.event-card .product-rating {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.2em;
    gap: 0.5em;
    font-size: 1.2em;
}

.event-card .mb-rating {
    display: flex;
    align-items: center;
    gap: 0.125em;
}

.event-card .mb-start-fill,
.event-card .mb-start {
    width: 0.875em;
    height: 0.875em;
    flex-shrink: 0;
}

.event-card .mb-start-fill {
    color: var(--warning);
}

.event-card .mb-start {
    color: var(--light-grey);
}

.event-card .product-rating__selled {
    font-size: 0.75em;
    color: var(--light-grey);
    white-space: nowrap;
}

/* Event Card Badge */
.event-card__badge {
    position: absolute;
    top: -3em;
    right: -2em;
    color: var(--title);
    padding: 2em;
    border-radius: 50%;
    font-size: 1.25em;
    font-weight: 700;
    text-align: center;
    line-height: 1.2;
    min-width: 3.5em;
    min-height: 3.5em;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 2;
}

.event-card__badge img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
    width: 100%;
    height: 100%;
    animation: rotate-360 20s linear infinite;
}

/* Hero Section */
.deals-hero {
    height: 100%;
}

.deals-hero__media {
    position: relative;
    height: 100%;
    min-height: 30em;
    border-radius: 0.5em;
    overflow: hidden;
    font-size: 1.1em;
}

.deals-hero__media img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    -webkit-mask-image: url(/static/images/products/bg-media.svg);
    mask-image: url(/static/images/products/bg-media.svg);
    -webkit-mask-size: auto 100%;
    mask-size: auto 100%;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    object-fit: cover;
    -webkit-mask-position: left;
    mask-position: left;
    color: transparent;
}

.deals-hero__content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    z-index: 2;
    max-width: 35em;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.deals-hero__title {
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 0.5em;
    line-height: 1.2;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    line-clamp: 4;
    -webkit-box-orient: vertical;
}

.deals-hero__subtitle {
    font-size: 1.25em;
    margin-bottom: 1.5em;
    opacity: 0.9;
}

.deals-hero__btn {
    display: inline-block;
    background: var(--white);
    color: var(--title);
    padding: 0.875em 2em;
    border-radius: 0.375em;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.deals-hero__btn:hover {
    background: var(--dark-light);
    transform: translateY(-0.125em);
    box-shadow: 0 0.25em 0.5em rgba(var(--rgb-black), 0.15);
}

/* Animation Text */
.deals-hero__text {
    position: absolute;
    bottom: 0em;
    left: 50%;
    transform: translateX(-50%);
    width: fit-content;
    height: fit-content;
    min-width: 30em;
    max-width: 90%;
    pointer-events: none;
    z-index: 1;
    font-size: 1em;
    overflow: visible;
}

.deals-hero__text text {
    text-transform: uppercase;
    animation: stroke 5s infinite alternate;
    stroke-width: 0.03em;
    stroke: var(--white);
    font-size: 4em;
    font-weight: 600;
    font-family: var(--font-family-title);
    opacity: 0.5;
    fill: rgba(var(--rgb-white), 0);
    white-space: nowrap;
}

/* Keyframes */
@keyframes rotate-360 {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(359deg);
    }
}

@keyframes stroke {
    0% {
        fill: rgba(var(--rgb-white), 0);
        stroke: var(--white);
        stroke-dashoffset: 25%;
        stroke-dasharray: 0 50%;
        stroke-width: 0.03em;
    }

    70% {
        fill: rgba(var(--rgb-white), 0);
        stroke: var(--white);
        stroke-width: 0.03em;
    }

    80% {
        fill: rgba(var(--rgb-white), 0);
        stroke: var(--white);
        stroke-width: 0.05em;
    }

    100% {
        fill: var(--white);
        stroke: rgba(var(--rgb-white), 0);
        stroke-dashoffset: -25%;
        stroke-dasharray: 50% 0;
        stroke-width: 0em;
    }
}

/* Responsive */
@media (max-width: 992px) {
    .deals-event {
        font-size: 1.25vw;
    }

    .deals-hero__content {
        width: 30em;
    }

    .deals-event__row {
        flex-direction: column-reverse;
    }

    .deals-event__products {
        width: 100%;
        height: 100%;
    }

    .deals-event__hero {
        width: 100%;
    }

    .deals-event__products .swiper {
        width: 100%;
    }

    .event-card__badge {
        top: -4em;
        right: -0.5em;
    }

    .deals-hero__media img {
        -webkit-mask-size: 100%;
        mask-size: 100%;
        object-fit: cover;
    }

    .deals-event__grid {
        width: 100%;
        grid-template-columns: repeat(2, 1fr);
    }

    /* Swiper responsive */

    .deals-hero__title {
        font-size: 2em;
    }

    .deals-hero__text text {
        font-size: 3.5em;
        stroke-width: 0.025em;
    }

    .event-card .mb-start-fill,
    .event-card .mb-start {
        width: 0.8em;
        height: 0.8em;
    }
}

@media (max-width: 768px) {
    .deals-event {
        font-size: 1.5vw;
    }

    .deals-hero__content {
        width: 30em;
    }

    .deals-event__row {
        flex-direction: column-reverse;
    }

    .deals-event__products {
        width: 100%;
    }

    /* Swiper responsive */

    .deals-hero__media img {
        -webkit-mask-size: 100%;
        mask-size: 100%;
        object-fit: cover;
    }

    .deals-event__grid {
        width: 100%;
        grid-template-columns: repeat(2, 1fr);
    }

    .deals-hero__title {
        font-size: 2em;
    }

    .deals-hero__text text {
        font-size: 3.5em;
        stroke-width: 0.025em;
    }

    .event-card .mb-start-fill,
    .event-card .mb-start {
        width: 0.8em;
        height: 0.8em;
    }
}

@media (max-width: 576px) {
    .deals-event {
        font-size: 2vw;
        overflow: hidden;
    }

    .deals-hero__content {
        width: 30em;
        font-size: 1.3em;
    }

    .deals-event__row {
        flex-direction: column-reverse;
    }

    .deals-event__products {
        width: 100%;
    }

    /* Swiper responsive */

    .deals-hero__media img {
        -webkit-mask-size: 100%;
        mask-size: 100%;
        object-fit: cover;
    }

    .deals-event__grid {
        width: 100%;
        grid-template-columns: repeat(2, 1fr);
    }

    .deals-hero__title {
        font-size: 2em;
    }

    .deals-hero__text text {
        font-size: 3.5em;
        stroke-width: 0.025em;
    }

    .event-card .mb-start-fill,
    .event-card .mb-start {
        width: 0.8em;
        height: 0.8em;
    }

    .deals-event__hero {
        font-size: 0.85em;
    }

    .event-card__content {
        font-size: 1.2em;
    }

    .deals-hero__text {
        bottom: -6em;
        left: 50%;
        transform: translateX(-50%);
    }
}

/* Deals Banner Section */
#deals-banner {
    aspect-ratio: 1920 / 300;
    width: 100%;
}

/* Deals Background Section */
.deals-bg {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 18.75em;
    position: relative;
    display: flex;
    align-items: center;
    padding: 5em 0;
    font-size: var(--10px);
}

.deals-bg .container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 75em;
    margin: 0 auto;
    padding: 0 0.9375em;
}

.deals-bg .content {
    margin-bottom: 1.875em;
    text-align: left;
    color: inherit;
}

.deals-bg .title {
    font-size: 4em;
    font-weight: 700;
    text-align: center;
    margin-bottom: 0.5em;
    color: var(--white);
    text-shadow: 0.125em 0.125em 0.25em rgba(var(--rgb-black), 0.5);
}

/* Breadcrumb Section */
.deals-breadcrumb {
    background: var(--white);
    padding: 1.25em 0;
    border-bottom: 0.0625em solid #e5e5e5;
    font-size: 0.652vw;
}

.deals-breadcrumb .list {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

.deals-breadcrumb .item {
    color: var(--title);
    font-size: 1.2em;
    font-weight: 500;
}

.deals-breadcrumb .item+.item::before {
    content: "";
    display: inline-block;
    width: 1.25em;
    height: 1.25em;
    margin: 0 0.625em;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z'%3E%3C/path%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.7;
    vertical-align: middle;
}

.deals-breadcrumb .link {
    color: var(--title);
    text-decoration: none;
    transition: color 0.3s ease;
}

.deals-breadcrumb .link:hover {
    color: var(--primary);
    text-decoration: underline;
}

/* Deals Similar Products Section */
.deals-similar {
    padding: 4em 0;
    overflow: hidden;
    font-size: var(--10px);
    width: 100%;
}

.deals-similar__layout {
    display: flex;
    align-items: center;
    gap: 3em;
    width: 100%;
}

.deals-similar__media {
    flex: 4;
}

.deals-similar__image {
    position: relative;
    border-radius: 50% 50% 0 0;
    aspect-ratio: 1;
    transition: all 0.8s;
    max-width: 100%;
    height: auto;
    width: 100%;
}

.deals-similar__image:after {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    border: 1px solid #000;
    z-index: 1;
    border-radius: 50% 50% 0 0;
    box-sizing: border-box;
    transition: all 0.8s;
}

.deals-similar__image:hover:after {
    left: -1.125em;
    top: 1.125em;
}

.deals-similar__image img {
    border-radius: 50% 50% 0 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.8s;
    z-index: 2;
    position: relative;
}

.deals-similar__image:hover img {
    transform: translate(0.625em, -0.625em);
}

.deals-similar__content {
    flex: 8;
    width: 0;
    min-width: 0;
}

.deals-similar__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    width: 100%;
}

.deals-similar__title {
    font-size: 3.2em;
    font-weight: 700;
    color: var(--title);
    margin: 0;
    line-height: 1.2;
}

.deals-similar__btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 8.75em;
    height: 8.75em;
    background: transparent;
    border-radius: 50%;
    text-decoration: none;
    color: var(--title);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.deals-similar__btn:hover {
    background: var(--title);
    color: var(--white);
}

.deals-similar__btn:hover .deals-similar__text-char {
    color: var(--white);
}

.deals-similar__btn:hover .deals-similar__arrow path {
    fill: var(--white);
}

.deals-similar__text-row {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 8.75em;
    height: 8.75em;
    background: transparent;
    border-radius: 100%;
    animation: rotate-text 20s linear infinite;
    text-align: center;
    position: relative;
}

@keyframes rotate-text {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(1turn);
    }
}

.deals-similar__text-char {
    display: block;
    position: absolute;
    top: 0;
    left: calc(50% - 4.375em);
    height: 4.375em;
    width: 8.75em;
    text-transform: uppercase;
    font-weight: 500;
    font-size: 1em;
    transform-origin: bottom center;
    transform: rotate(var(--char-rotate));
    color: var(--title);
    transition: var(--transition);
}

.deals-similar__arrow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2.5em;
    height: 2.5em;
    z-index: 2;
}

.deals-similar__arrow path {
    transition: var(--transition);
}

/* Swiper Container */
.deals-swiper {
    width: 100%;
    height: auto;
    position: relative;
    /* padding: 0 3em; */
    overflow: hidden;
}

.deals-swiper .swiper-wrapper {
    display: flex;
    width: 100%;
}

.deals-swiper .swiper-slide {
    height: auto;
    display: flex;
}

/* Custom Navigation - Similar to flashsale__next */
.deals-swiper .swiper-button-next,
.deals-swiper .swiper-button-prev {
    width: 4em;
    height: 4em;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.2);
    font-size: var(--10px);
    margin-top: calc(-2em);
    transition: var(--transition);
}

.deals-swiper .swiper-button-next:after,
.deals-swiper .swiper-button-prev:after {
    font-size: 2em;
    color: rgb(var(--rgb-white));
}

.deals-swiper .swiper-button-next:hover,
.deals-swiper .swiper-button-prev:hover {
    background-color: rgba(0, 0, 0, 0.4);
}

.deals-swiper .swiper-button-next {
    right: 0;
}

.deals-swiper .swiper-button-prev {
    left: 0;
}

/* Cards */
.deals-card {
    background: var(--white);
    border-radius: 0.5em;
    overflow: hidden;
    box-shadow: var(--shadow-1);
    transition: var(--transition);
    width: 100%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    font-size: 1.5em;
}

.deals-card__media {
    position: relative;
    overflow: hidden;
    border-radius: 0.5em 0.5em 0 0;
}

.deals-card__media img {
    width: 100%;
    height: 20em;
    object-fit: cover;
    transition: var(--transition);
}

.deals-card:hover .deals-card__media img,
.swiper-slide-active .deals-card__media img {
    transform: scale(1.05);
}

/* Ribbon Badge */
.deals-card__media {
    overflow: hidden;
}

.deals-card__ribbon {
    position: absolute;
    left: -3.125em;
    top: 0.8em;
    padding: 0.3125em 0.75em;
    color: var(--white);
    font-size: 0.8125em;
    font-weight: 600;
    z-index: 2;
    background: var(--primary);
    transition: all 0.5s ease;
}

.deals-card__ribbon::before {
    content: "";
    position: absolute;
    right: -1.0625em;
    top: 0;
    border: 0.875em solid transparent;
    border-left-color: var(--primary);
    border-top-color: var(--primary);
}

.deals-card__ribbon::after {
    content: "";
    position: absolute;
    right: -1.0625em;
    bottom: 0;
    border: 0.875em solid transparent;
    border-left-color: var(--primary);
    border-bottom-color: var(--primary);
}

.deals-card:hover .deals-card__ribbon,
.swiper-slide-active .deals-card__ribbon {
    left: -0.0625em;
}

.deals-card__ribbon-text {
    transition: all 0.5s ease;
    opacity: 0;
}

.deals-card:hover .deals-card__ribbon-text,
.swiper-slide-active .deals-card__ribbon-text {
    opacity: 1;
}

.deals-card__content {
    padding: 1em;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.deals-card__brand {
    font-size: 0.75em;
    color: var(--light-grey);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25em;
    font-weight: 500;
}

.deals-card__title {
    font-size: 0.875em;
    font-weight: 400;
    margin: 0 0 0.75em 0;
    color: var(--title);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.deals-card__title a {
    color: inherit;
    text-decoration: none;
    transition: var(--transition);
}

.deals-card__title a:hover {
    color: var(--primary);
}

.deals-card__price {
    display: flex;
    align-items: center;
    gap: 0.5em;
    margin: 0;
}

.deals-card__price-current {
    font-size: 1em;
    font-weight: 600;
    color: var(--primary);
}

.deals-card__price-original {
    font-size: 0.875em;
    color: var(--light-grey);
    text-decoration: line-through;
    font-weight: 400;
}

.deals-card__discount {
    font-size: 0.75em;
    color: var(--primary);
    font-weight: 600;
    margin-left: auto;
}

/* Product Rating in Deals Card */
.deals-card .product-rating {
    margin-top: 0.5em;
    font-size: 0.75em;
}

.deals-card .mb-rating {
    gap: 0.125em;
}

.deals-card .mb-start-fill,
.deals-card .mb-start {
    width: 0.875em;
    height: 0.875em;
}

.deals-card .product-rating__selled {
    font-size: 1em;
    font-weight: 400;
    color: var(--light-grey);
}

/* Responsive */
@media (max-width: 1199px) {
    .deals-similar__layout {
        gap: 2em;
    }
}

@media (max-width: 992px) {
    .deals-similar {
        font-size: 1vw;
        padding: 2em 0;
    }

    .deals-similar__layout {
        flex-direction: row;
        gap: 2em;
    }

    .deals-similar__media {
        flex: 4;
    }

    .deals-swiper .swiper-button-next,
    .deals-swiper .swiper-button-prev {
        width: 3em;
        height: 3em;
        margin-top: calc(-1.5em);
    }

    .deals-swiper .swiper-button-next:after,
    .deals-swiper .swiper-button-prev:after {
        font-size: 1.5em;
    }

    .deals-similar__header {
        flex-direction: row;
        gap: 1em;
        text-align: center;
    }

    .deals-similar__title {
        font-size: 1.75em;
    }

    .deals-card__media img {
        aspect-ratio: 1;
    }

    .deals-card {
        font-size: 1.3em;
    }
}

@media (max-width: 768px) {
    .deals-similar {
        font-size: 1.25vw;
        padding: 2em 0;
    }

    .deals-similar__layout {
        flex-direction: column;
        gap: 2em;
    }
    .deals-similar__btn-wrap{
        font-size: 1.5em;
    }
    .deals-similar__media {
        flex: none;
        width: 100%;
    }

    .deals-similar__content {
        flex: none;
        width: 100%;
        min-width: 0;
    }

    .deals-similar__title-wrap {
        width: 75%;
        text-align: left;
    }
    .deals-similar__btn-wrap {
        font-size: 1.25em;
    }
    .deals-similar__header {
        flex-direction: row;
        gap: 1em;
        text-align: center;
        font-size: 0.75em;
    }

    .deals-similar__title {
        font-size: 3em;
    }

    .deals-card__media img {
        aspect-ratio: 3 / 4;
        height: auto;
    }

    .deals-card {
        font-size: 1.5em;
    }
    .deals-card__title {
        font-size: 1.1em;
        margin: 0 0 0.25em 0;
    }
    .deals-card__ribbon {
        font-size: 0.85em;
    }
    .deals-card .product-rating {
        margin-top: 0.25em;
        font-size: 1em;
    }
    .deals-card__price {
        font-size: 1.1em;
    }

    .deals-card__price-original {
        font-size: 0.7em;
    }

    .deals-card .product-rating__selled {
        font-size: 0.7em;
    }

    .deals-bg {
        padding: 2.5em 0 1.25em;
        min-height: 12.5em;
        font-size: 2vw;
    }

    .deals-breadcrumb {
        font-size: 2vw;
    }

    .deals-bg .title {
        font-size: 1.75em;
    }

    .deals-bg .container {
        padding: 0 0.625em;
    }
}

@media (max-width: 576px) {
    .deals-similar {
        font-size: 2.5vw;
        padding: 2em 0;
    }

    .deals-similar__layout {
        flex-direction: column;
        gap: 2em;
    }

    .deals-similar__media {
        flex: none;
        width: 100%;
    }

    .deals-similar__content {
        flex: none;
        width: 100%;
        min-width: 0;
    }

    .deals-similar__title-wrap {
        width: 75%;
        text-align: left;
    }

    .deals-similar__header {
        flex-direction: row;
        gap: 1em;
        text-align: center;
        font-size: 0.75em;
    }

    .deals-similar__title {
        font-size: 2em;
    }

    .deals-card__media img {
        aspect-ratio: 3 / 4;
        height: auto;
    }

    .deals-card {
        font-size: 1.2em;
    }
    .deals-card__title {
        font-size: 1.1em;
        margin: 0 0 0.25em 0;
    }
    .deals-card__ribbon {
        font-size: 0.85em;
    }
    .deals-card .product-rating {
        margin-top: 0.25em;
        font-size: 1em;
    }
    .deals-card__price {
        font-size: 1.1em;
    }

    .deals-card__price-original {
        font-size: 0.7em;
    }

    .deals-card .product-rating__selled {
        font-size: 0.7em;
    }

    .deals-bg {
        padding: 2.5em 0 1.25em;
        min-height: 12.5em;
        font-size: 2vw;
    }

    .deals-breadcrumb {
        font-size: 2vw;
    }

    .deals-bg .title {
        font-size: 1.75em;
    }

    .deals-bg .container {
        padding: 0 0.625em;
    }
}