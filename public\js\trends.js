const trends = (function () {
  new Swiper(".trends-product__swiper", {
    loop: true,
    autoplay: {
      delay: 3000,
      disableOnInteraction: true,
    },
    navigation: {
      nextEl: ".trends-product__next",
      prevEl: ".trends-product__prev",
    },
    spaceBetween: calcSpaceBetweenFromVW(1.5),
    breakpoints: {
      768: {
        slidesPerView: 4,
      },
    },
  });
  function calcSpaceBetweenFromVW(em) {
    const fs = (window.innerWidth * 0.652) / 100;
    if (fs > 20) return 20;
    return em * fs;
  }
  return {
    init: function () {},
  };
})();

document.addEventListener("DOMContentLoaded", function () {
  trends.init();
});
