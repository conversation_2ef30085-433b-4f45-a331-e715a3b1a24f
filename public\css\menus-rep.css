/* 
* <576:     xs
* 576-768:  sm
* 768-992:  md
* 992-1200: lg
* 1200 -1400: xl
* >= 1400:  xxl
*/
@media (max-width: 575px){
    .xs-hidden{
        display: none;
    }
    .xs-block{
        display: block;
    }
    .xs-news-w100{
        width: 100%;
    }
    .xs-box-w100{
        max-width: 100%;
    }       

}
@media (min-width: 576px) and (max-width: 768px){
  
}

@media (max-width: 768px){ 
   
}

@media only screen and (min-width: 769px) and (max-width: 1024px) {
  
}

@media only screen and (min-width: 992px) and (max-width: 1199px){
   
    
}

@media only screen and (min-width: 1200px) and (max-width: 1399px){
    
}