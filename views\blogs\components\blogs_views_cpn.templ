package components

templ BlogsViewsCpn() {
	<section class="container90 grid-wrapper">
		<div class="grid-wrapper__title">
			<span class="grid__analytic">BÀI VIẾT ĐƯỢC QUAN TÂM</span>
		</div>
		<div class="row-wrapper"> 
        // Chỉ load 3 bài viết có lượt xem cao nhất
			@blogViewsItem("Cách chọn trang phục phù hợp với dáng người",
				"/tin-tuc/chon-trang-phuc-phu-hop-dang-nguoi.html", "/static/images/blogs/blog-office-style.jpg", "3.850",
				"1.200")
			@blogViewsItem("Xu hướng màu sắc thời trang Thu Đông 2025", "/tin-tuc/xu-huong-mau-sac-thu-dong-2025.html",
				"/static/images/blogs/blog-trend-spring.png", "4.100", "1.450")
			@blogViewsItem("<PERSON><PERSON> quyết phố<PERSON> đồ từ cơ bản đến nâng cao", "/tin-tuc/bi-quyet-phoi-do-co-ban-nang-cao.html",
				"/static/images/blogs/blog-minimalist.jpg", "2.950", "980")
		</div>
	</section>
}

templ blogViewsItem(title, pagePath, img, views, likes string) {
	<article class="nitem nitem-row hover-gray">
		<div class="nitem__thumb nitem__thumb--row">
			<a href={ templ.SafeURL(pagePath) } title={ title } class="nitem__thumb-link">
				<img class="nitem__thumb-img nitem__thumb-img--col" src={ img } alt={ title }/>
			</a>
		</div>
		<div class="nitem__content nitem__content--row">
			<h3 class="nitem__title nitem__title__grid">
				<a class="txt-hover-green" href={ templ.SafeURL(pagePath) } title={ title }>{ title }</a>
			</h3>
			<div class="nitem-row__bottom">
				<ul class="nitem-row__support">
					<li class="nitem__support-item">
						<div class="nitem__support-icon">
							<svg width="12" height="11" viewBox="0 0 12 11" fill="none">
								<use xlink:href="#icon-like-blog"></use>
							</svg>
						</div>
						<span>{ likes }</span>
					</li>
					<li class="nitem__support-item">
						<div class="nitem__support-icon">
							<svg width="12" height="11" viewBox="0 0 12 11" fill="none">
								<use xlink:href="#icon-eye-blog"></use>
							</svg>
						</div>
						<span>{ views }</span>
					</li>
				</ul>
			</div>
		</div>
	</article>
}
