package components

import (
    "goweb/views/partials"
)


templ FlashsaleCpn() {
<section class="flashsale">
    <div class="container90">
        <div class="flashsale__wrapper">
            <div class="flashsale__header">
                <div class="flashsale__countdown">
                    <h3 class="countdown__title">Kết thúc trong</h3>
                    <div class="countdown__timer" data-flashsale data-time-end="2025-09-26T15:00:00+07:00"></div>
                </div>
                <h2 class="flashsale__title">FLASH SALE</h2>                
            </div>
            <div class="flashsale__content">
                <div class="swiper flashsale__swiper">
                    <div class="swiper-wrapper">
                        {{z:=1}}
                        for i:=1;i<=5; i++ {
                            @partials.ProductItem(" swiper-slide product--p10")
                            {{z = z+3}}
                        }
                    </div>
                    <div class="swiper-button-next flashsale__next"></div>
                    <div class="swiper-button-prev flashsale__prev"></div>
                </div>   
            </div>
            <div class="flashsale__footer">
                <a href="#" title="Xem chi tiết" class="btn-primary"><span>Xem tất cả</span></a>
            </div>            
        </div>
    </div>
</section>
}