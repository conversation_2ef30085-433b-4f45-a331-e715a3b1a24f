.product__container {
    width: 100%;
    margin: 0 auto;
    padding: 2vw 0;
}

.product__container {
    font-size: 0.729vw;
    margin-bottom: var(--vw-14);
}


.product-grid {
    display: grid;
    justify-content: center;
    grid-template-columns: repeat(4, var(--vw-300));
    gap: var(--vw-24);
}


.product-card {
    background-color: transparent;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: transform 0.2s ease;
    position: relative;
    width: var(--vw-300);
}

.product-card .btn__quickview {
    border-radius: var(--vw-20);
}



.product-card .product__images {
    position: relative;
    width: var(--vw-300);
    height: var(--vw-400);
    overflow: hidden;
    border-radius: var(--vw-20);
}


.product__images .product__tag {
    position: absolute;
    top: var(--vw-16);
    left: var(--vw-16);
    z-index: 10;
    font-size: var(--vw-12);
}

.product__tag .badge {
    padding: var(--vw-5) var(--vw-14);
    background-color: white;
    color: black;
    font-size: var(--vw-12);
    text-transform: uppercase;
    text-align: center;
    font-weight: 600;
    border-radius: var(--vw-16);
    transition: 0.5s;
}


.product__images>img {
    position: absolute;
    left: 0;
    top: 0;
    transition: opacity 0.2s ease;
    opacity: 1;

}

.product__images>.img--hover {

    opacity: 0;
    transition: opacity 0.2s ease;

}


.product-card .product__images:hover>.img--hover {
    opacity: 1;
    transition: transform 0.5s ease;
    transform: scale(1.1, 1.1);
}

.product-card .product__images>.img--active {
    opacity: 1;
    transition: transform 0.2s ease;
}


.product-card:hover .product-card__icon {
    opacity: 1;
    transform: translateX(0);

}


.product-card__icons {
    font-size: var(--vw-32);
    display: flex;
    flex-direction: column;
    position: absolute;
    background-color: transparent;
    right: var(--vw-16);
    top: var(--vw-16);
    z-index: 10;


}

.product-card__icon:nth-child(1) {
    transition-delay: 0s;
}

.product-card__icon:nth-child(2) {
    transition-delay: 0.1s;
}

.product-card__icon {
    width: var(--vw-40);
    height: var(--vw-40);
    cursor: pointer;
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
    background-color: white;
    border-radius: 50%;
    margin-bottom: var(--vw-10);
    font-size: var(--vw-24);
    position: relative;

}

.product-card__icon .icon__heart {
    position: absolute;
    top: var(--vw-5);
    left: var(--vw-8);
    position: absolute;


}

.product-card__icon .icon__cart {
    top: var(--vw-4);
    left: var(--vw-9);
    position: absolute;


}

.product-card__icon:hover {
    background-color: black;
    color: white;

}



/* .product-card:hover {
    transform: scale(1.02);
} */


.product-card__top {
    position: relative;
    overflow: hidden;
}

.product-card__button {
    opacity: 0;
    transform: translateY(0);
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    display: flex;
    justify-content: center;
    position: absolute;
    z-index: 10;
    width: 100%;

}


.product-card:hover .product-card__button {
    opacity: 1;
    transform: translateY(-2.604vw);

}


.product-card:hover .product-card__image.product-card__image--hover {
    display: block;

}

.product-card:hover .product-card__image {
    display: none;

}

.product-card__image--hover {
    display: none;
}

.product-card__image {
    width: 100%;
    height: auto;
    object-fit: cover;
    transition-duration: 700ms;
}

.product-card__body {
    padding: var(--vw-10) 0;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    width: 100%;
}


.product-card__title h3 a {
    font-size: var(--vw-18);
    line-height: 1.4;
    margin: 0;
}

.product-card__title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: initial;
    overflow: hidden;
    margin: var(--vw-8) 0;
    font-size: var(--vw-18);
    font-weight: normal;
    line-height: 1.4;
    height: var(--vw-50);
    /* min-height: 35px; */
}



.product-card__price {
    font-size: var(--vw-16);
    font-weight: 500;
    margin: var(--vw-4) 0;
    height: var(--vw-24);
    position: relative;
    display: flex;
    align-items: center;

}

.product-card__price span {
    font-size: var(--vw-16);
    font-weight: 500;
}


.product-card__price .old__price {
    text-decoration: line-through;
    font-size: var(--vw-14);
    font-weight: 500;
    position: absolute;
    right: 0;
}

.product-card__price span.price__saleoff {
    font-size: var(--vw-18);
    font-weight: 500;
    color: var(--primary);
    margin-top: var(--vw-4);
}

.product-card__price .price__discount {
    margin-left: var(--vw-6);
    width: max-content;
    font-size: var(--vw-12);
    padding: var(--vw-2);
    border: var(--vw-3) solid #ff379b;
    border-radius: var(--vw-10) var(--vw-10) var(--vw-10) 0;
    display: inline-block;
    color: #ff379b;
    font-weight: 700;


}

.product-card__price .price__discount .price__percent {
    font-size: var(--vw-10);
    font-weight: 700;
}


.product-card__star {
    height: var(--vw-24);
    margin: var(--vw-4) 0;
    align-items: center;
    display: flex;
    gap: var(--vw-8);
}

.product-card__sold span {
    font-size: var(--vw-12);
    font-weight: 500;
    color: #67686C;
}


.star__item {
    background: url("../images/svg/start_deactive.svg") repeat-x left center;
    height: 14px;
    width: 65px;
}

.star__item--active {
    background: url("../images/svg/start_active.svg") repeat-x left center;
    height: 14px;
}

.product-card__color {
    height: var(--vw-24);
    margin: var(--vw-4) 0;
}

.product-card__color .color__list {
    display: flex;
    gap: 0.5vw;
    margin-bottom: 0.5vw;
}

.product-card__color .color__item {
    width: 1.5vw;
    height: 1.5vw;
    padding: var(--vw-4);
    border-radius: 50%;

    cursor: pointer;
}

.color__link {
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #000;
    cursor: pointer;
}


.product-card__color .color__item--active {
    width: 1.5vw;
    height: 1.5vw;
    border-radius: 50%;
    border: var(--vw-1) solid rgba(var(--rgb-black), 0.2);
}



.modal.modal__product {
    width: var(--vw-1024);
}

.modal__product__saleoff {
    display: flex;

}

.modal__product__name {
    font-size: var(--vw-24);
}

.modal__product__image {
    max-width: var(--vw-520);
    max-height: var(--vw-520);
    overflow: hidden;
}

.modal__body .product__action i {
    font-size: var(--vw-20);
    margin-right: var(--vw-4);
}




/* Mobile: 1 item trên mỗi hàng */
@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: 1fr;
        justify-content: center;
        /* hoặc stretch nếu muốn full width */
    }

    .product-card {
        width: 100%;
        /* hoặc max-width: 90vw nếu cần */
        margin: 0 auto;
    }

    .product-card .product__images {
        width: 300px;
        min-height: 400px;
    }

    .product-card .product-card__body {
        width: 300px;
        height: 150px;
    }

    .product-card__title {
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 5px;
    }



    .product-card__color .color__list {
        display: flex;
        gap: 0.5vw;
        margin-bottom: 0.5vw;
    }

    .product-card__color .color__item {
        width: 24px;
        height: 24px;
        padding: 4px;
        border-radius: 50%;
        border: 1px solid #000;
        cursor: pointer;
    }
}